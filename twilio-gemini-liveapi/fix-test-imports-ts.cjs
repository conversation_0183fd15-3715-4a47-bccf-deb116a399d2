const fs = require('fs');
const path = require('path');

const testDir = './test';
const files = fs.readdirSync(testDir).filter(f => f.endsWith('.test.js'));

files.forEach(file => {
  const filePath = path.join(testDir, file);
  let content = fs.readFileSync(filePath, 'utf-8');
  
  // Fix imports from ../src/ - change .js back to .ts
  const regex = /from '\.\.\/src\/([^']+)\.js'/g;
  content = content.replace(regex, function(match, p1) {
    return "from '../src/" + p1 + ".ts'";
  });
  
  fs.writeFileSync(filePath, content);
  console.log('Fixed imports in ' + file);
});

console.log('Done fixing imports to .ts');