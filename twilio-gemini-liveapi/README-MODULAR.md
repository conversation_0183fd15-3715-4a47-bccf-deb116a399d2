# Claude AI Assistant Guide for Twilio Gemini Live API Project

## Project Overview
This is a Node.js voice AI call center system integrating Twilio telephony with Google Gemini Live API for conversational AI. The project uses a modular TypeScript/JavaScript architecture with comprehensive testing.

## Directory Structure & File Organization

### Source Code Organization (`src/`)
The `src/` directory contains all core application logic organized by functional domains:

#### API Layer (`src/api/`)
- `management.ts` - Campaign and session management endpoints
- `routes.ts` - Main API route definitions and middleware setup
- `testing.ts` - Testing and debugging endpoints for development

#### Audio Processing (`src/audio/`)
- `audio-forwarding.ts` - Real-time audio stream forwarding between Twilio and Gemini
- `audio-processor.ts` - Audio format conversion (μ-law ↔ PCM16) and processing
- `transcription-manager.ts` - Audio transcription handling and management

#### Configuration Management (`src/config/`)
- `config.ts` - Main configuration loader with environment validation
- `audio-config.ts` - Audio processing settings and codec configurations
- `business-config.ts` - Business logic and workflow configuration
- `campaign-config.ts` - Campaign script settings and management
- `localization-config.ts` - Language, locale, and internationalization settings

#### Context Management (`src/context/`)
- `conversation-context-manager.js` - Conversation state and context management

#### AI Integration (`src/gemini/`)
- `client.ts` - Main Google Gemini Live API client implementation
- `model-manager.ts` - AI model configuration and selection
- `voice-manager.ts` - Voice synthesis settings and voice selection

#### Middleware (`src/middleware/`)
- `auth-simple.js` - Basic authentication middleware
- `security-utils.js` - Input validation, sanitization, and security utilities

#### Campaign Scripts (`src/scripts/`)
- `script-manager.js` - Campaign script loading and management
- `script-cache.js` - Script caching system for performance
- `incoming-converter.js` - Inbound call script conversion utilities

#### Session Management (`src/session/`)
- `session-manager.ts` - Main session orchestrator and lifecycle management
- `context-manager.ts` - Session-specific context handling
- `health-monitor.ts` - Session health monitoring and diagnostics
- `lifecycle-manager.ts` - Session creation, maintenance, and destruction
- `recovery-manager.ts` - Session recovery mechanisms and error handling
- `summary-manager.ts` - Call summary generation and storage

#### Type Definitions (`src/types/`)
- `global.d.ts` - Global TypeScript type declarations
- `websocket.d.ts` - WebSocket-specific type definitions
- `custom/` - Custom type definitions for project-specific interfaces

#### Utilities (`src/utils/`)
- `logger.js` - Structured logging utility with emoji prefixes
- `test-utils.js` - Testing helper functions and mock utilities
- `twilio-validation.js` - Twilio webhook signature validation
- `websocket-utils.js` - WebSocket connection helper functions
- `dotenv-stub.js` - Environment variable handling utilities

#### WebSocket Handling (`src/websocket/`)
- `handlers.ts` - Main WebSocket message router and dispatcher
- `audio-handlers.ts` - Audio stream processing and forwarding
- `config-handlers.ts` - Configuration message handling
- `heartbeat-manager.ts` - Connection health monitoring and keepalive
- `local-testing-handler.ts` - Browser-based testing support
- `session-events.ts` - Session event handling and broadcasting
- `session-utils.ts` - Session-related utility functions
- `start-session.ts` - Session initialization and setup
- `twilio-flow-handler.ts` - Twilio-specific call flow handling

### Testing Organization (`test/`)
All tests are located in the `test/` directory using Node.js built-in test runner:

#### Test Structure
- `test/helpers/` - Shared test utilities and environment setup
- `test/[component].test.js` - Component-specific test files
- Tests cover all 4 major flows: outbound/inbound × Twilio/browser

#### Key Test Files
- `configuration.test.js` - Configuration system validation (27+ tests)
- `backend-api.test.js` - API endpoint testing
- `workflow-integration.test.js` - End-to-end workflow testing
- `session-manager.test.js` - Session lifecycle testing
- `audio-processor.test.js` - Audio processing validation

### Frontend (`call-center-frontend/`)
Next.js application with standard structure:
- `app/` - Next.js 13+ app router pages and layouts
- `lib/` - Frontend utility functions and configurations
- `public/` - Static assets
- Standard Next.js configuration files

## File Naming Conventions

### Source Files
- **TypeScript**: `.ts` extension for new modules
- **JavaScript**: `.js` extension for legacy modules (being migrated)
- **Naming**: kebab-case for files, camelCase for functions, PascalCase for classes

### Test Files
- **Pattern**: `[component-name].test.js`
- **Location**: `test/` directory (root level)
- **Helpers**: `test/helpers/` for shared utilities

## Development Guidelines

### Where to Put New Code

#### New API Endpoints
- Add to `src/api/routes.ts` for main endpoints
- Add to `src/api/management.ts` for admin/management features
- Add to `src/api/testing.ts` for development/debugging endpoints

#### Audio Processing Features
- Core processing: `src/audio/audio-processor.ts`
- Stream handling: `src/audio/audio-forwarding.ts`
- Transcription: `src/audio/transcription-manager.ts`

#### Configuration Changes
- Main config: `src/config/config.ts`
- Domain-specific: appropriate `src/config/[domain]-config.ts` file

#### Session Logic
- Core orchestration: `src/session/session-manager.ts`
- Lifecycle events: `src/session/lifecycle-manager.ts`
- Error recovery: `src/session/recovery-manager.ts`

#### WebSocket Features
- Message routing: `src/websocket/handlers.ts`
- Audio handling: `src/websocket/audio-handlers.ts`
- New flow types: create new handler in `src/websocket/`

#### Utility Functions
- General utilities: `src/utils/`
- Test helpers: `test/helpers/`

### Testing Requirements

#### Test Coverage
- **All 27+ tests must pass** before committing
- Each major component requires corresponding test file
- Include both unit tests and integration tests
- Test all 4 flows: outbound/inbound × Twilio/browser

#### Test Location
- Place tests in `test/` directory
- Use descriptive names: `[component-name].test.js`
- Group related tests in the same file
- Use `test/helpers/` for shared test utilities

#### Test Structure
```javascript
import { test, describe } from 'node:test';
import assert from 'node:assert';
// Component-specific tests with proper setup/teardown
```

## Code Quality Standards

### Before Committing
1. Run `npm run lint:fix` - Fix all linting issues
2. Run `npm test` - All 27+ tests must pass
3. Test manually with all 4 flows
4. Verify logs use proper emoji prefixes and structured format

### File Size Limits
- **Keep files under 500 lines** - split large files into modules
- Extract reusable functions to `src/utils/`
- Break up large classes into smaller, focused classes
- Separate concerns: business logic, API routes, utilities

### Import/Export Rules
- Use ES modules (`import`/`export`) throughout
- No CommonJS (`require`/`module.exports`)
- Group imports: external packages, internal modules, relative imports
- Import from `src/` for internal modules

## Common Development Tasks

### Adding a New API Endpoint
1. Define route in `src/api/routes.ts`
2. Implement handler in appropriate `src/api/` file
3. Add validation using `src/middleware/security-utils.js`
4. Create test in `test/backend-api.test.js`

### Adding Audio Processing Feature
1. Implement in `src/audio/audio-processor.ts`
2. Update audio configuration in `src/config/audio-config.ts`
3. Add WebSocket handling in `src/websocket/audio-handlers.ts`
4. Create test in `test/audio-processor.test.js`

### Adding Session Management Feature
1. Implement in appropriate `src/session/` module
2. Update session manager in `src/session/session-manager.ts`
3. Add WebSocket events in `src/websocket/session-events.ts`
4. Create test in `test/session-manager.test.js`

### Adding Configuration Option
1. Add to `src/config/config.ts` or domain-specific config file
2. Update environment variable validation
3. Document in `.clinerules` file
4. Add test in `test/configuration.test.js`

## Architecture Principles

### Modular Design
- Single responsibility per module
- Clear separation of concerns
- Dependency injection where appropriate
- Minimal coupling between modules

### Error Handling
- Always wrap async operations in try-catch
- Use structured logging with context
- Implement graceful degradation
- Include recovery mechanisms

### Performance
- Use connection pooling for external APIs
- Implement caching where appropriate
- Monitor memory usage for long-running sessions
- Clean up inactive sessions and contexts

This guide provides the foundation for understanding and contributing to the Twilio Gemini Live API project. Always refer to `.clinerules` for the most current development standards and requirements.
