const fs = require('fs');
const path = require('path');

const testDir = './test';
const files = fs.readdirSync(testDir).filter(f => f.endsWith('.test.js'));

files.forEach(file => {
  const filePath = path.join(testDir, file);
  let content = fs.readFileSync(filePath, 'utf-8');
  
  // Fix imports from ../src/
  content = content.replace(/from '\.\.\/src\/([^']+)'/g, (match, p1) => {
    if (!p1.endsWith('.js')) {
      return "from '../src/" + p1 + ".js'";
    }
    return match;
  });
  
  fs.writeFileSync(filePath, content);
  console.log('Fixed imports in ' + file);
});

console.log('Done fixing imports');
