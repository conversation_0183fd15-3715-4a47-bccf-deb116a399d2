import 'dotenv/config';
import Fastify, { FastifyInstance } from 'fastify';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import fastifyCompress from '@fastify/compress';
import fastifyRateLimit from '@fastify/rate-limit';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';

// Import modular components
import { initializeGeminiClient } from './src/gemini/client';
import { voiceManager } from './src/gemini/voice-manager';
import { modelManager } from './src/gemini/model-manager';
import { ContextManager } from './src/session/context-manager';
import { SessionManager } from './src/session/session-manager';
import { ConnectionHealthMonitor } from './src/session/health-monitor';
import { SessionRecoveryManager } from './src/session/recovery-manager';
import { SessionSummaryManager } from './src/session/summary-manager';
import { SessionLifecycleManager } from './src/session/lifecycle-manager';
import { ScriptManager } from './src/scripts/script-manager';
import { AudioProcessor } from './src/audio/audio-processor';
import { TranscriptionManager } from './src/audio/transcription-manager';
import { globalHeartbeatManager } from './src/websocket/heartbeat-manager';
import { registerWebSocketHandlers } from './src/websocket/handlers';
import { registerApiRoutes } from './src/api/routes';
import { registerManagementRoutes } from './src/api/management';
import { registerTestingRoutes } from './src/api/testing';
import { validateSharedAuth } from './src/middleware/shared-auth';
import { registerErrorHandlers, registerErrorLoggingHook } from './src/middleware/error-handler';
import { logger } from './src/utils/logger';
import { timerManager } from './src/utils/timer-manager';
import { BoundedMap } from './src/utils/bounded-map';
import { memoryMonitor } from './src/utils/memory-monitor';

// Import types
import type { ConnectionData, AppConfig } from './src/types/global';

// Get directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load configuration system
import { config, validateConfig } from './src/config/config';

// Validate configuration on startup
validateConfig();

// Health check routes registration
function registerHealthCheckRoutes(fastify: FastifyInstance, dependencies: any): void {
    // Basic health check
    fastify.get('/health', async (_request, reply) => {
        const memoryHealth = memoryMonitor.getHealthSummary();
        const healthStatus = {
            status: memoryHealth.status === 'ok' ? 'healthy' : 'degraded',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: {
                ...process.memoryUsage(),
                status: memoryHealth.status,
                memoryMB: memoryHealth.memoryMB,
                heapUsedMB: memoryHealth.heapUsedMB
            },
            version: process.version,
            environment: process.env.NODE_ENV || 'development'
        };

        const statusCode = memoryHealth.status === 'critical' ? 503 : 200;
        return reply.code(statusCode).send(healthStatus);
    });

    // Detailed health check with component status
    fastify.get('/health/detailed', async (_request, reply) => {
        const {
            activeConnections,
            sessionManager,
            contextManager,
            lifecycleManager,
            recoveryManager,
            healthMonitor
        } = dependencies;

        const detailedHealth = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            components: {
                activeConnections: {
                    status: 'healthy',
                    count: activeConnections?.size || 0,
                    maxSize: activeConnections?.maxSize || 'unknown'
                },
                sessionManager: {
                    status: sessionManager ? 'healthy' : 'unavailable',
                    metrics: sessionManager ? 'available' : 'unavailable'
                },
                contextManager: {
                    status: contextManager ? 'healthy' : 'unavailable',
                    contexts: contextManager?.getContextStats?.() || 'unavailable'
                },
                lifecycleManager: {
                    status: lifecycleManager ? 'healthy' : 'unavailable'
                },
                recoveryManager: {
                    status: recoveryManager ? 'healthy' : 'unavailable',
                    metrics: recoveryManager?.getRecoveryMetrics?.() || 'unavailable'
                },
                healthMonitor: {
                    status: healthMonitor ? 'healthy' : 'unavailable'
                }
            }
        };

        reply.code(200).send(detailedHealth);
    });

    // Readiness check (for Kubernetes/Docker)
    fastify.get('/ready', async (_request, reply) => {
        const { activeConnections, sessionManager } = dependencies;

        const isReady = !!(activeConnections && sessionManager);

        if (isReady) {
            reply.code(200).send({ status: 'ready', timestamp: new Date().toISOString() });
        } else {
            reply.code(503).send({ status: 'not ready', timestamp: new Date().toISOString() });
        }
    });

    // Liveness check (for Kubernetes/Docker)
    fastify.get('/live', async (_request, reply) => {
        reply.code(200).send({ status: 'alive', timestamp: new Date().toISOString() });
    });
}

// Enhanced environment variable validation
function validateEnvironmentVariables(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const requiredVars = [
        'GEMINI_API_KEY',
        'TWILIO_ACCOUNT_SID',
        'TWILIO_AUTH_TOKEN',
        'PUBLIC_URL'
    ];

    const optionalVars = [
        'DEEPGRAM_API_KEY',
        'SUMMARY_GENERATION_PROMPT',
        'AI_PREPARE_MESSAGE'
    ];

    // Check required variables
    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            errors.push(`Missing required environment variable: ${varName}`);
        }
    }

    // Warn about optional variables
    for (const varName of optionalVars) {
        if (!process.env[varName]) {
            logger.warn(`⚠️ Optional environment variable not set: ${varName}`);
        }
    }

    // Validate URL format
    if (process.env.PUBLIC_URL && !process.env.PUBLIC_URL.match(/^https?:\/\/.+/)) {
        errors.push('PUBLIC_URL must be a valid HTTP/HTTPS URL');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// Validate environment variables
const envValidation = validateEnvironmentVariables();
if (!envValidation.isValid) {
    logger.error('❌ Environment validation failed:', envValidation.errors);
    process.exit(1);
}
logger.info('✅ Environment variables validated successfully');

// Global error handlers for production safety
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('🚨 Unhandled Promise Rejection', { promise, reason, stack: reason?.stack });
    
    // Log additional context for debugging
    logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    
    // In production, might want to restart after cleanup
    if (process.env.NODE_ENV === 'production') {
        logger.error('🚨 Production environment: initiating graceful shutdown');
        process.exit(1);
    }
});

process.on('uncaughtException', (error: Error) => {
    logger.error('🚨 Uncaught Exception', { error });
    logger.error('🚨 Process info', {
        pid: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime()
    });
    
    // Uncaught exceptions are always fatal
    logger.error('🚨 Fatal error: process will exit');
    process.exit(1);
});

process.on('warning', (warning: any) => {
    logger.warn('⚠️ Node.js Warning', { name: warning.name, message: warning.message, stack: warning.stack });
});

// Get default voice and model from configuration
const GEMINI_DEFAULT_VOICE: string = config.ai.gemini.defaultVoice;
const GEMINI_DEFAULT_MODEL: string = config.ai.gemini.defaultModel;

// Get other configuration constants
const SUMMARY_GENERATION_PROMPT: string = config.prompts.summaryGeneration;
const AI_PREPARE_MESSAGE: string = config.prompts.aiPrepareOutbound;
const TWILIO_ACCOUNT_SID: string = config.auth.twilio.accountSid;
const TWILIO_AUTH_TOKEN: string = config.auth.twilio.authToken;
const PUBLIC_URL: string = config.server.publicUrl;
const PORT: number = config.server.port;

logger.info('🚀 Twilio Gemini Live API Server starting...');
logger.info(`📝 Using Gemini API Key: ${config.auth.gemini.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`📞 Twilio Config: ${config.auth.twilio.accountSid ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`🎤 Deepgram API Key: ${config.auth.deepgram.apiKey ? 'SET ✅' : 'NOT SET ❌'}`);
logger.info(`🔗 Public URL: ${config.server.publicUrl || 'NOT SET'}`);
logger.info(`🤖 Default Model: ${GEMINI_DEFAULT_MODEL}`);
logger.info(`🎵 Default Voice: ${GEMINI_DEFAULT_VOICE}`);

// Initialize core components
const geminiClient = initializeGeminiClient(config.auth.gemini.apiKey);
const contextManager = new ContextManager();

// Tracks active connections with automatic cleanup when capacity is reached
const activeConnections = new BoundedMap<string, ConnectionData>(1000); // Increased from 500 to 1000 concurrent connections
// Pass activeConnections reference
const sessionManager = new SessionManager(contextManager, geminiClient, activeConnections);
const healthMonitor = new ConnectionHealthMonitor();
const summaryManager = new SessionSummaryManager();
const lifecycleManager = new SessionLifecycleManager(contextManager, healthMonitor, summaryManager);
// Set the lifecycle manager reference for cleanup
activeConnections.setLifecycleManager(lifecycleManager);
const recoveryManager = new SessionRecoveryManager(contextManager, healthMonitor, sessionManager);
const scriptManager = new ScriptManager();
const audioProcessor = new AudioProcessor();
const transcriptionManager = new TranscriptionManager();

// Initialize Fastify server
const fastify: FastifyInstance = Fastify({ logger: false });

// Register error handlers
registerErrorHandlers(fastify);
registerErrorLoggingHook(fastify);

// Register plugins
await fastify.register(fastifyWs);
await fastify.register(fastifyCors, {
    origin: process.env.NODE_ENV === 'production'
        ? ['https://twilio-gemini.verduona.com', 'https://gemini-api.verduona.com', 'http://localhost:3011']
        : ['http://localhost:3000', 'http://localhost:3011', 'http://localhost:3101'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    optionsSuccessStatus: 200 // Use 200 instead of 204 for better compatibility
});
await fastify.register(fastifyFormBody);

// Register authentication middleware
fastify.addHook('preHandler', validateSharedAuth);

// Enhanced CORS headers handling for development - AFTER auth middleware
fastify.addHook('onRequest', async (request, reply) => {
    const origin = request.headers.origin;
    const allowedOrigins = process.env.NODE_ENV === 'production'
        ? ['https://twilio-gemini.verduona.com', 'https://gemini-api.verduona.com', 'http://localhost:3011']
        : ['http://localhost:3000', 'http://localhost:3011', 'http://localhost:3101'];
    
    // Always set CORS headers for all requests from allowed origins
    if (origin && allowedOrigins.includes(origin)) {
        reply.header('Access-Control-Allow-Origin', origin);
        reply.header('Access-Control-Allow-Credentials', 'true');
        reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
        reply.header('Access-Control-Max-Age', '3600');
    }
    
    // Handle preflight OPTIONS requests early
    if (request.method === 'OPTIONS') {
        if (origin && allowedOrigins.includes(origin)) {
            reply.header('Access-Control-Allow-Origin', origin);
            reply.header('Access-Control-Allow-Credentials', 'true');
            reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept');
            reply.header('Access-Control-Max-Age', '3600');
        }
        return reply.code(200).send();
    }
});

// Add security headers middleware
fastify.addHook('onSend', async (request, reply, payload) => {
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    if (process.env.NODE_ENV === 'production') {
        reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        reply.header('Content-Security-Policy',
            'default-src \'self\'; ' +
            'connect-src \'self\' wss: ws:; ' +
            'script-src \'self\' \'unsafe-inline\'; ' +
            'style-src \'self\' \'unsafe-inline\'; ' +
            'media-src \'self\' blob:; ' +
            'font-src \'self\';'
        );
    }
    
    return payload;
});

// Register compression for better performance
await fastify.register(fastifyCompress, {
    global: true,
    threshold: 1024 // Only compress responses larger than 1KB
});

// Register rate limiting for security
await fastify.register(fastifyRateLimit, {
    max: 100, // Maximum 100 requests
    timeWindow: '1 minute', // Per minute
    skipOnError: true // Don't count failed requests
});

// Register static file serving for management interfaces
if (!fastify.hasReplyDecorator || !fastify.hasReplyDecorator('sendFile')) {
    await fastify.register(fastifyStatic, {
        root: path.join(__dirname, 'static'),
        prefix: '/static/'
    });
} else {
    logger.warn('⚠️ Static routes already registered - skipping duplicate registration');
}

// Dependencies object for modules
const dependencies = {
    config,
    geminiClient,
    contextManager,
    sessionManager,
    healthMonitor,
    summaryManager,
    lifecycleManager,
    recoveryManager,
    scriptManager,
    audioProcessor,
    transcriptionManager,
    activeConnections,
    voiceManager,
    modelManager,
    GEMINI_DEFAULT_VOICE,
    GEMINI_DEFAULT_MODEL,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL
};

// Register health check endpoints first
logger.info('🔧 Registering health check endpoints...');
registerHealthCheckRoutes(fastify, dependencies);
logger.info('✅ Health check endpoints registered');

// Register API routes first so decorators like getNextCallConfig are available
logger.info('🔧 Calling registerApiRoutes...');
try {
    registerApiRoutes(fastify, dependencies);
    logger.info('✅ API routes registration completed');
} catch (error) {
    logger.error('❌ Error in registerApiRoutes', { error: error as Error });
    throw error;
}

// Expose getNextCallConfig to WebSocket handlers
if (typeof fastify.getNextCallConfig === 'function') {
    (dependencies as any).getNextCallConfig = fastify.getNextCallConfig;
}
if (fastify.callConfigEmitter) {
    (dependencies as any).callConfigEmitter = fastify.callConfigEmitter;
}

// Register WebSocket handlers after API routes
logger.info('🔧 Registering WebSocket handlers...');
registerWebSocketHandlers(fastify, dependencies);
logger.info('✅ WebSocket handlers registered');

// Register management routes
logger.info('🔧 Registering management routes...');
registerManagementRoutes(fastify, dependencies);
logger.info('✅ Management routes registered');

// Register testing routes
logger.info('🔧 Registering testing routes...');
registerTestingRoutes(fastify, dependencies);
logger.info('✅ Testing routes registered');

// === MANAGEMENT INTERFACE ROUTES ===

// Incoming call management interface (secured)
fastify.get('/incoming', { preHandler: validateSharedAuth }, async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'incoming-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        logger.error('❌ Error loading incoming call management interface', { error: error as Error });
        return reply.code(500).send({ error: 'Failed to load incoming call management interface' });
    }
});

// Outbound scripts management interface (secured)
fastify.get('/outbound-scripts', { preHandler: validateSharedAuth }, async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'outbound-scripts-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        logger.error('❌ Error loading outbound scripts management interface', { error: error as Error });
        return reply.code(500).send({ error: 'Failed to load outbound scripts management interface' });
    }
});

// Redirect route for backward compatibility
fastify.get('/scripts', async (_request, reply) => {
    return reply.redirect('/outbound-scripts');
});

// Preload campaign scripts into cache
await scriptManager.preloadScripts();

// Cleanup old contexts and sessions periodically using timer manager
timerManager.setInterval('global_cleanup', () => {
    contextManager.cleanupOldContexts();
    lifecycleManager.cleanup();
}, 300000); // Every 5 minutes

// Start the server
const start = async (): Promise<void> => {
    try {
        await fastify.listen({
            port: PORT,
            host: '0.0.0.0'
        });
        logger.info(`🚀 Server listening on port ${PORT}`);
        logger.info(`🔗 WebSocket endpoint: ws://localhost:${PORT}/media-stream`);
        logger.info(`🧪 Local audio testing: ws://localhost:${PORT}/local-audio-session`);
        logger.info(`🏥 Health check: http://localhost:${PORT}/health`);
        logger.info(`📊 API documentation: http://localhost:${PORT}/`);

        // Start memory monitoring in production
        if (process.env.NODE_ENV === 'production') {
            memoryMonitor.startMonitoring();
            logger.info('📊 Memory monitoring started for production');
        }

        // Start memory monitoring in production
        if (process.env.NODE_ENV === 'production') {
            memoryMonitor.startMonitoring();
            logger.info('📊 Memory monitoring started');
        }

        // Log voice and model configuration
        modelManager.logConfiguration();
        const voiceInfo = voiceManager.getVoiceInfo(GEMINI_DEFAULT_VOICE)?.characteristics || 'unknown';
        logger.info(`🎤 Default Voice: ${GEMINI_DEFAULT_VOICE} (${voiceInfo})`);
        logger.info(`🎵 Available Voices: ${Object.keys(voiceManager.getAvailableVoices()).join(', ')}`);
        logger.info(`🔧 Voice Selection: ${voiceManager.isVoiceSelectionEnabled() ? 'Enabled' : 'Disabled'}`);

        // Graceful shutdown handlers
        const gracefulShutdown = async (signal: string): Promise<void> => {
            logger.info(`\n🛑 Received ${signal}, initiating graceful shutdown...`);

            try {
                await performShutdownSequence();
                logger.info('✅ Graceful shutdown completed');
                process.exit(0);
            } catch (error) {
                logger.error('❌ Error during graceful shutdown', { error: error as Error });
                process.exit(1);
            }
        };

        const performShutdownSequence = async (): Promise<void> => {
            // Stop accepting new connections
            logger.info('🔒 Stopping server from accepting new connections...');
            await fastify.close();

            // Clean up all manager resources
            logger.info('🧹 Cleaning up session management resources...');

            // Stop all WebSocket heartbeats
            globalHeartbeatManager.stopAllHeartbeats();

            // Clean up managers
            await cleanupManagers();

            // Clean up all active sessions
            await cleanupActiveSessions();

            // Clean up periodic intervals using timer manager
            logger.info('🗑️ Clearing periodic intervals...');
            timerManager.clearAll();

            // Clean up all Maps and Sets
            logger.info('🗑️ Clearing data structures...');
            activeConnections.clear();
        };

        const cleanupManagers = async (): Promise<void> => {
            const managers = [
                { obj: healthMonitor, method: 'stopHealthChecks', name: 'healthMonitor' },
                { obj: contextManager, method: 'clearAllContexts', name: 'contextManager' },
                { obj: recoveryManager, method: 'cleanup', name: 'recoveryManager' },
                { obj: summaryManager, method: 'cleanup', name: 'summaryManager' },
                { obj: transcriptionManager, method: 'cleanup', name: 'transcriptionManager' }
            ];

            for (const { obj, method, name } of managers) {
                if (obj && (obj as any)[method]) {
                    try {
                        (obj as any)[method]();
                    } catch (error) {
                        logger.warn(`⚠️ Error cleaning up ${name}`, { error });
                    }
                }
            }

            // Session manager specific cleanup
            if (sessionManager) {
                for (const [sessionId] of (sessionManager as any).sessionMetrics) {
                    sessionManager.cleanupSession(sessionId);
                }
            }
        };

        const cleanupActiveSessions = async (): Promise<void> => {
            if (!lifecycleManager) {
                return;
            }

            const activeSessions = lifecycleManager.getActiveSessions();
            logger.info(`🔚 Ending ${activeSessions.length} active sessions...`);

            for (const sessionId of activeSessions) {
                try {
                    const connectionData = activeConnections.get(sessionId);
                    await (lifecycleManager as any).forceEndSession(
                        sessionId, connectionData, 'server_shutdown'
                    );
                } catch (error) {
                    logger.warn(`⚠️ Error ending session ${sessionId}`, { error: error as Error });
                }
            }
        };

        // Register shutdown handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

        // Handle PM2 graceful reload
        process.on('message', (msg: any) => {
            if (msg === 'shutdown') {
                gracefulShutdown('PM2_SHUTDOWN');
            }
        });

        logger.info('✅ Server is ready to handle calls!');
    } catch (err) {
        logger.error('❌ Error starting server', { error: err as Error });
        process.exit(1);
    }
};

start();
