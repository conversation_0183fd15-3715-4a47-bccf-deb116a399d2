# 🚀 Production Deployment Guide

## For Roo Code AI Assistants

This guide provides step-by-step deployment instructions. **Always use the automated script unless troubleshooting.**

## Quick Start

To deploy the Twilio Gemini Live API system to production, simply run:

```bash
./deploy-production.sh
```

This automated script handles all critical production setup including:
- PM2 log rotation (prevents disk full crashes)
- Memory limits (prevents OOM restarts)
- Frontend production build
- Service restart with proper configuration
- Health checks and verification

## Manual Deployment Steps

If the automated script fails or you need manual control:

### 1. Install PM2 Log Rotation
```bash
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 50M
pm2 set pm2-logrotate:retain 7
pm2 set pm2-logrotate:compress true
```

### 2. Build Frontend
```bash
cd call-center-frontend
npm run build
cd ..
```

### 3. Start Services
```bash
pm2 start ecosystem.config.cjs --only twilio-gemini-backend
pm2 start ecosystem.config.cjs --only twilio-gemini-frontend
```

### 4. Save PM2 State
```bash
pm2 save
pm2 startup  # Follow the instructions provided
```

## Critical Issues Addressed

### 1. Log File System Crash Prevention
- **Issue**: 212MB log file with no rotation causes disk full
- **Solution**: PM2 log rotation with 50MB max size, 7 days retention

### 2. Memory Leak Prevention
- **Issue**: 337 restarts due to memory issues
- **Solution**: 1GB memory limit per service with cluster mode

### 3. Session Recovery
- **Issue**: Failed calls cannot be recovered
- **Solution**: Proper session cleanup and recovery mechanisms

## Health Checks

After deployment, verify:
```bash
# Check service status
pm2 status

# Check backend health
curl https://gemini-api.verduona.com/health

# Check frontend
curl -I http://localhost:3011

# View logs with emoji prefixes
pm2 logs --lines 20

# Run comprehensive health check
./health-check.sh
```

## 🔍 Monitoring & Debugging (Essential for Roo Code AI Assistants)

### Understanding the Logging System

All logs use **emoji prefixes** for easy identification:
- 🚀 **Startup/initialization events**
- 📞 **Call-related events**
- 🎤 **Audio processing**
- 💬 **AI/Gemini responses**
- ❌ **Errors that need attention**
- ⚠️ **Warnings to monitor**
- ✅ **Successful operations**
- 🔍 **Debug information**
- 🔄 **Session lifecycle**
- 🌊 **WebSocket events**
- 🧹 **Cleanup and memory management**

### View Logs
```bash
# Backend logs with emoji prefixes for debugging
pm2 logs twilio-gemini-backend --lines 50

# Frontend logs
pm2 logs twilio-gemini-frontend --lines 50

# All services combined
pm2 logs --lines 100

# Filter by emoji for specific issues
pm2 logs twilio-gemini-backend | grep "🎤"  # Audio issues
pm2 logs twilio-gemini-backend | grep "📞"  # Call issues
pm2 logs twilio-gemini-backend | grep "💬"  # AI issues
pm2 logs twilio-gemini-backend | grep "❌"  # Errors only
pm2 logs twilio-gemini-backend | grep "🧹"  # Memory/cleanup
```

### Real-time Monitoring
```bash
# Interactive dashboard
pm2 monit

# View metrics
pm2 info twilio-gemini-backend
pm2 info twilio-gemini-frontend

# Check specific logs
pm2 logs --json  # JSON format for parsing
```

## Troubleshooting

If services fail to start:
1. Check logs: `pm2 logs --err`
2. Verify environment variables in `.env`
3. Ensure ports 3101 and 3011 are available
4. Check nginx configuration if using reverse proxy

## Emergency Recovery

If the system crashes:
```bash
# Quick recovery
pm2 resurrect

# Full restart
./deploy-production.sh
```

## Time Estimate

Full deployment takes approximately 5-10 minutes:
- Dependencies installation: 2-3 minutes
- Frontend build: 2-3 minutes
- Service startup: 1 minute

## 📋 Quick Reference for Roo Code

### Most Common Commands
```bash
# Deploy everything
./deploy-production.sh

# Check health
./health-check.sh

# View logs
pm2 logs --lines 50

# Restart after issues
pm2 restart all
```

### Debugging Workflow
1. Check health: `./health-check.sh`
2. View logs: `pm2 logs --lines 100`
3. Filter by emoji for specific issues
4. Check memory/CPU: `pm2 monit`
5. Restart if needed: `pm2 restart all`

### Important Files for AI Assistants
- `.roorules` - Complete project rules and instructions
- `ROO_CODE_GUIDE.md` - Quick reference guide
- `CLAUDE.md` - Code simplicity guidelines
- `ecosystem.config.cjs` - PM2 configuration

Remember to always test in a staging environment before deploying to production!
- Health verification: 1 minute