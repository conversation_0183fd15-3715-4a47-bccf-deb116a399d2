import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Get authorization header from the incoming request
    const authHeader = request.headers.get('authorization');
    
    const headers: any = {};
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    // Save to backend
    await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/save-config`, data, { headers });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save configuration' }, 
      { status: 500 }
    );
  }
}