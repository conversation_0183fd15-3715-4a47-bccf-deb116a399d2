import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Mock health data for the provider health monitor
    // Use deterministic values to prevent hydration mismatches
    const now = new Date();
    const baseTime = Math.floor(now.getTime() / 10000) * 10000; // Round to 10-second intervals
    const seed = baseTime % 1000; // Use time-based seed for consistent "randomness"

    const healthData = {
      gemini: {
        status: 'healthy',
        latency: 50 + (seed % 100), // Deterministic "random" 50-150ms
        uptime: '99.9%',
        lastCheck: new Date(baseTime).toISOString()
      },
      twilio: {
        status: 'healthy',
        latency: 25 + (seed % 50), // Deterministic "random" 25-75ms
        uptime: '99.8%',
        lastCheck: new Date(baseTime).toISOString()
      },
      system: {
        status: 'healthy',
        cpu: 10 + (seed % 30), // Deterministic "random" 10-40%
        memory: 30 + (seed % 40), // Deterministic "random" 30-70%
        uptime: Math.floor(baseTime / 1000) - 86400, // Fixed uptime calculation
        lastCheck: new Date(baseTime).toISOString()
      }
    };

    return NextResponse.json(healthData);
  } catch (error) {
    console.error('Error fetching provider health:', error);
    return NextResponse.json(
      { error: 'Failed to fetch provider health' },
      { status: 500 }
    );
  }
}
