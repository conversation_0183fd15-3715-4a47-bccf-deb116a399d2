import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Mock connection metrics data
    // Use deterministic values to prevent hydration mismatches
    const now = new Date();
    const baseTime = Math.floor(now.getTime() / 10000) * 10000; // Round to 10-second intervals
    const seed = baseTime % 1000; // Use time-based seed for consistent "randomness"

    const metrics = {
      success: true,
      timestamp: new Date(baseTime).toISOString(),
      metrics: {
        connections: {
          active: 1 + (seed % 10),
          total: 50 + (seed % 100),
          failed: seed % 5
        },
        audioQuality: {
          peakLevel: (seed % 100),
          averageLevel: 20 + ((seed * 2) % 60),
          silencePercentage: (seed % 20),
          clippingPercentage: (seed % 5)
        },
        latency: {
          average: 50 + (seed % 100),
          min: 10 + (seed % 50),
          max: 100 + (seed % 200)
        },
        bandwidth: {
          upload: 500 + (seed % 1000),
          download: 1000 + (seed % 2000)
        }
      }
    };

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Error fetching connection metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch connection metrics' },
      { status: 500 }
    );
  }
}
