import { NextRequest, NextResponse } from 'next/server';

/**
 * Token validation endpoint for AuthGuard
 * This route validates tokens by checking with the shared auth-service
 * Integrated with Verduona shared authentication system
 */
export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();
    
    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Get auth service URL from environment (shared auth-service)
    const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3999';
    
    console.log('🔍 [Twilio-Gemini] Validating token with shared auth-service:', authServiceUrl);
    
    // Validate token with shared auth-service
    const authResponse = await fetch(`${authServiceUrl}/status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!authResponse.ok) {
      console.log('❌ [Twilio-Gemini] Shared auth service validation failed:', authResponse.status);
      return NextResponse.json(
        { error: 'Token validation failed' },
        { status: 401 }
      );
    }

    const authData = await authResponse.json();
    
    if (!authData.authenticated) {
      console.log('❌ [Twilio-Gemini] Token not authenticated by shared auth-service:', authData);
      return NextResponse.json(
        { error: 'Token not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ [Twilio-Gemini] Token validated successfully with shared auth-service');

    // Return success with user info in shared auth format
    return NextResponse.json({
      authenticated: true,
      user: authData.user ? {
        id: authData.user.id,
        email: authData.user.email,
        role: authData.user.role || 'authenticated',
        isAuthenticated: true
      } : null
    });

  } catch (error) {
    console.error('🔥 [Twilio-Gemini] Token validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}