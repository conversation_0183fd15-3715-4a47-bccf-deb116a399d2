import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Call backend endpoint
    await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/make-call`, data);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to start calls:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to start calls' }, 
      { status: 500 }
    );
  }
}