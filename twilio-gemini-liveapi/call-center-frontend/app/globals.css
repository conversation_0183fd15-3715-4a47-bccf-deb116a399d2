@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles with Verduona Green Theme */
@layer base {
  :root {
    /* Light mode - Premium complementary green palette for work under the sun */
    --background: 155 10% 99%;  /* Subtle green-tinted background instead of pure white */
    --foreground: 155 40% 20%;  /* More pronounced green-black text for readability */
    --card: 0 0% 100%;  /* Pure white cards for contrast against tinted background */
    --card-foreground: 155 40% 20%;  /* More pronounced green text on cards */
    --popover: 155 15% 98%;  /* Consistent with cards */
    --popover-foreground: 155 40% 20%;  /* More pronounced green popover text */

    /* Primary: Bright green that works well in sunlight */
    --primary: 155 60% 35%;  /* Same bright green as dark mode for consistency */
    --primary-foreground: 0 0% 100%;  /* White text on green */

    /* Secondary: Light green-gray for supporting elements */
    --secondary: 155 15% 95%;  /* Very light green for secondary elements */
    --secondary-foreground: 155 40% 25%;  /* More pronounced green text */

    /* Muted: Subtle green background for non-interactive elements */
    --muted: 155 15% 95%;  /* Consistent with secondary */
    --muted-foreground: 155 20% 40%;  /* Medium green for muted content */

    /* Accent: Teal that stands out in light mode */
    --accent: 175 70% 40%;  /* Slightly darker teal for light mode */
    --accent-foreground: 0 0% 100%;  /* White text on teal */

    /* Status colors optimized for light green theme */
    --success: 145 70% 35%;  /* Consistent green for success */
    --success-foreground: 0 0% 100%;
    --warning: 38 90% 45%;  /* Amber for warnings */
    --warning-foreground: 0 0% 100%;
    --destructive: 0 80% 45%;  /* Red for errors */
    --destructive-foreground: 0 0% 100%;

    /* UI elements with subtle green tint */
    --border: 155 15% 85%;  /* Light green-tinted borders */
    --input: 155 15% 97%;  /* Very light green input backgrounds */
    --ring: 155 60% 35%;  /* Green focus rings (same as primary) */
    --radius: 0.5rem;  /* Border radius */

    /* Enhanced contrast for interactive elements */
    --badge-text: 155 40% 20%;  /* More pronounced green badge text for light mode */
    --icon-muted: 155 30% 45%;  /* Medium green for muted icons in light mode */
    --table-text: 155 40% 20%;  /* More pronounced green for table text in light mode */
  }

  .dark {
    /* Dark mode - Ergonomic green palette inspired by Russian cockpit design */
    --background: 155 20% 8%;  /* Deep green-black background for eye comfort */
    --foreground: 150 10% 98%;  /* High contrast white text */
    --card: 155 25% 12%;  /* Slightly lighter green for cards */
    --card-foreground: 150 10% 98%;  /* High contrast card text */
    --popover: 155 25% 12%;  /* Consistent with cards */
    --popover-foreground: 150 10% 98%;  /* High contrast popover text */

    /* Primary: Ergonomic green that reduces eye strain */
    --primary: 155 60% 35%;  /* Bright green for primary actions */
    --primary-foreground: 0 0% 100%;  /* White text on green */

    /* Secondary: Darker green-gray for supporting elements */
    --secondary: 155 25% 18%;  /* Medium green for secondary elements */
    --secondary-foreground: 150 10% 98%;  /* High contrast text */

    /* Muted: Subtle background for non-interactive elements */
    --muted: 155 25% 18%;  /* Consistent with secondary */
    --muted-foreground: 150 10% 85%;  /* Slightly dimmed text for muted content */

    /* Accent: Teal that stands out in dark mode */
    --accent: 175 70% 35%;  /* Teal accent for highlights */
    --accent-foreground: 0 0% 100%;  /* White text on teal */

    /* Status colors optimized for dark green theme */
    --success: 145 70% 35%;  /* Brighter green for success */
    --success-foreground: 0 0% 100%;
    --warning: 38 90% 45%;  /* Amber for warnings */
    --warning-foreground: 0 0% 100%;
    --destructive: 0 80% 45%;  /* Red for errors */
    --destructive-foreground: 0 0% 100%;

    /* UI elements with green tint */
    --border: 155 25% 20%;  /* Green-tinted borders */
    --input: 155 25% 20%;  /* Green-tinted input backgrounds */
    --ring: 155 60% 35%;  /* Green focus rings */

    /* Enhanced contrast for interactive elements */
    --badge-text: 150 10% 95%;  /* High contrast badge text */
    --icon-muted: 150 10% 80%;  /* Better contrast for muted icons */
    --table-text: 150 10% 85%;  /* Better contrast for table text */
  }

  html, body, #root {
    @apply h-full; /* Ensure full height */
  }

  * {
    border-color: hsl(var(--border));  /* Apply border color to all elements with border */
  }

  body {
    background-color: hsl(var(--background));  /* Apply background color */
    color: hsl(var(--foreground));  /* Apply text color */
    font-feature-settings: "rlig" 1, "calt" 1;
    overflow-x: hidden; /* Prevent horizontal scroll */
    scroll-behavior: smooth; /* Smooth scrolling */
    @apply antialiased;
  }

  html {
    overflow-x: hidden; /* Prevent horizontal scroll at root level */
    scroll-behavior: smooth; /* Smooth scrolling */
  }

  #root {
    @apply flex flex-col; /* Set up flex column layout */
  }
}

/* Component Layer for Verduona-specific styles */
@layer components {
  /* Standardized Button Styles */
  .btn-primary {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transition-colors;
  }

  .btn-secondary {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transition-colors;
  }

  .btn-outline {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md border border-border bg-background text-foreground hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transition-colors;
  }

  /* Standardized Card Styles */
  .card {
    @apply bg-card border border-border rounded-lg shadow-sm text-card-foreground;
  }

  .card-header {
    @apply p-6 pb-3;
  }

  .card-title {
    @apply text-lg font-semibold text-card-foreground;
  }

  .card-description {
    @apply text-sm text-muted-foreground mt-1;
  }

  .card-content {
    @apply p-6 pt-3;
  }

  /* Gradient background with theme support */
  .gradient-bg {
    @apply bg-gradient-to-b from-background via-background to-muted;
  }

  /* Status badges */
  .badge-success {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-emerald-100 text-emerald-800 border border-emerald-200 dark:bg-emerald-950/40 dark:text-emerald-300 dark:border-emerald-800;
  }

  .badge-warning {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-orange-100 text-orange-800 border border-orange-200 dark:bg-orange-950/40 dark:text-orange-300 dark:border-orange-800;
  }

  .badge-error {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-red-100 text-red-800 border border-red-200 dark:bg-red-950/40 dark:text-red-300 dark:border-red-800;
  }

  /* Universal text colors */
  .text-status-success {
    @apply text-green-600 dark:text-green-400;
  }

  .text-status-warning {
    @apply text-yellow-600 dark:text-yellow-400;
  }

  .text-status-error {
    @apply text-red-600 dark:text-red-400;
  }

  .text-status-info {
    @apply text-primary;
  }
}

/* Ensure root element has proper theme class */
html {
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Loading state */
html.loading * {
  transition: none !important;
}

/* Enable transitions once loaded */
html:not(.loading) * {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Ensure form elements inherit theme colors */
input, textarea, select, option {
  color: hsl(var(--foreground));
  background-color: hsl(var(--input));
  border-color: hsl(var(--border));
}

input::placeholder,
textarea::placeholder {
  color: hsl(var(--muted-foreground));
}

/* Proper theme-aware styling with good contrast */
.dark input,
.dark select,
.dark textarea {
  background-color: hsl(var(--input));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}

/* Ensure smooth transitions for all theme changes */
input, select, textarea, body {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Consistent text selection highlighting */
::selection {
  background-color: hsl(var(--accent) / 0.3) !important;
  color: hsl(var(--accent-foreground)) !important;
  text-shadow: none !important;
}

::-moz-selection {
  background-color: hsl(var(--accent) / 0.3) !important;
  color: hsl(var(--accent-foreground)) !important;
  text-shadow: none !important;
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
  .demo-container {
    margin: 1rem !important;
    padding: 1rem !important;
  }
  
  input, textarea, select {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
  
  h1 {
    font-size: 1.5rem !important;
  }
}

/* iOS and Android specific fixes */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari */
  input, textarea, select {
    -webkit-appearance: none;
    border-radius: 0;
  }
}

/* Android Chrome specific */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select {
    background-image: none;
  }
}
