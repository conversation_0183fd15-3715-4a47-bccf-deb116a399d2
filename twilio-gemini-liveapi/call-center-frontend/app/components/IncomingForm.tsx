import { DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import IncomingCallInterface from './IncomingCallInterface';
import CallStatusSection from './CallStatusSection';

interface IncomingFormProps {
  AVAILABLE_COUNTRIES: { code: string; name: string; flag: string; phoneNumber: string }[];
  AVAILABLE_LANGUAGES: { code: string; name: string }[];
  availableModels: Record<string, unknown>;
  availableVoices: string[];
  voiceDetails: Record<string, any>;
  voiceDescriptions: Record<string, string>;
  selectedCountry: string;
  setSelectedCountry: (val: string) => void;
  selectedModel: string;
  setSelectedModel: (val: string) => void;
  selectedVoice: string;
  setSelectedVoice: (val: string) => void;
  selectedLanguage: string;
  setSelectedLanguage: (val: string) => void;
  aiInstructions: string;
  setAiInstructions: (val: string) => void;
  loadCampaignScript: (id: number) => void;
  audioMode: 'twilio' | 'local';
  isRecording: boolean;
  isProcessing: boolean;
  stopLocalTest: (index: number) => void;
  handleLocalTest: (company: {name: string; phone: string}, index: number) => void;
  fromPhoneNumber: string;
  incomingCallsEnabled: boolean;
  latestSummary: any | null;
  callStatus: string;
  lastError: string | null;
  audioStatus: string;
  aiInitTimer: number;
  isConnected: boolean;
}

export default function IncomingForm({
  AVAILABLE_COUNTRIES,
  AVAILABLE_LANGUAGES,
  availableModels,
  availableVoices,
  voiceDetails,
  voiceDescriptions,
  selectedCountry,
  setSelectedCountry,
  selectedModel,
  setSelectedModel,
  selectedVoice,
  setSelectedVoice,
  selectedLanguage,
  setSelectedLanguage,
  aiInstructions,
  setAiInstructions,
  loadCampaignScript,
  audioMode,
  isRecording,
  isProcessing,
  stopLocalTest,
  handleLocalTest,
  fromPhoneNumber,
  incomingCallsEnabled,
  latestSummary,
  callStatus,
  lastError,
  audioStatus,
  aiInitTimer,
  isConnected
}: IncomingFormProps) {
  return (
    <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
      <div>
        <label htmlFor="incomingCountrySelect" className="block text-sm font-medium mb-2 text-foreground">
          Select Country / Twilio Number
        </label>
        <select
          id="incomingCountrySelect"
          value={selectedCountry}
          onChange={(e) => setSelectedCountry(e.target.value)}
          className={cn(
            'w-full p-2 border border-border rounded-md shadow-sm',
            'focus:ring-primary focus:border-primary bg-input text-foreground',
            'transition-colors duration-200'
          )}
        >
          {AVAILABLE_COUNTRIES.map((country) => (
            <option key={country.code} value={country.code}>
              {country.flag} {country.name} ({country.phoneNumber})
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="incomingModelSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select AI Model
        </label>
        <select
          id="incomingModelSelect"
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          className={cn(
            'w-full p-2 border border-border rounded-md shadow-sm',
            'focus:ring-primary focus:border-primary bg-input text-foreground',
            'transition-colors duration-200'
          )}
        >
          {Object.entries(availableModels).map(([modelId, modelInfo]) => (
            <option key={modelId} value={modelId}>
              {typeof modelInfo === 'object' && modelInfo !== null && 'name' in modelInfo ? (modelInfo as { name: string }).name : String(modelInfo)}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="incomingVoiceSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select AI Voice Sound
        </label>
        <select
          id="incomingVoiceSelect"
          value={selectedVoice}
          onChange={(e) => setSelectedVoice(e.target.value)}
          className={cn(
            'w-full p-2 border border-border rounded-md shadow-sm',
            'focus:ring-primary focus:border-primary bg-input text-foreground',
            'transition-colors duration-200'
          )}
        >
          {availableVoices.map((voice) => {
            const details = voiceDetails[voice] as {
              gender?: string;
              characteristics?: string;
              description?: string;
              useCase?: string;
            } | undefined;
            const envDescription = voiceDescriptions[voice];
            const displayText = envDescription || (details
              ? `${voice} - ${details.gender || 'Unknown'} (${details.characteristics || 'No description'})`
              : voice.charAt(0).toUpperCase() + voice.slice(1));
            return (
              <option key={voice} value={voice} className="bg-input text-foreground">
                {displayText}
              </option>
            );
          })}
        </select>
      </div>

      <div>
        <label htmlFor="incomingLanguageSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select Output Language
        </label>
        <select
          id="incomingLanguageSelect"
          value={selectedLanguage}
          onChange={(e) => setSelectedLanguage(e.target.value)}
          className={cn(
            'w-full p-2 border border-border rounded-md shadow-sm',
            'focus:ring-primary focus:border-primary bg-input text-foreground',
            'transition-colors duration-200'
          )}
        >
          {AVAILABLE_LANGUAGES.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.name}
            </option>
          ))}
        </select>
      </div>

      <div className="space-y-3">
        <label className="block text-sm font-medium text-foreground">
          Load Incoming Campaign Script (Available Campaigns)
        </label>
        <div className="grid grid-cols-6 gap-2 text-xs font-semibold text-muted-foreground text-center">
          <div className="p-2 bg-accent/20 rounded">English Insurance</div>
          <div className="p-2 bg-accent/20 rounded">English Fundraising</div>
          <div className="p-2 bg-accent/20 rounded">Spanish Insurance</div>
          <div className="p-2 bg-accent/20 rounded">Spanish Fundraising</div>
          <div className="p-2 bg-accent/20 rounded">Czech Insurance</div>
          <div className="p-2 bg-accent/20 rounded">Czech Fundraising</div>
        </div>
        <div className="grid grid-cols-6 gap-2">
          {[7,8,9,10,11,12].map((id) => (
            <button
              key={id}
              type="button"
              onClick={() => loadCampaignScript(id)}
              className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
            >
              <DocumentArrowDownIcon className="h-4 w-4 inline mr-1" />
              {id - 6} (In)
            </button>
          ))}
        </div>
      </div>

      <div>
        <div className="flex items-center justify-between mb-2">
          <label htmlFor="incomingAiInstructionsDescription" className="block text-sm font-medium text-foreground">
            Campaign Script / AI Instructions (Text Format - JSON supported)
          </label>
        </div>
        <textarea
          id="incomingAiInstructionsDescription"
          value={aiInstructions}
          onChange={(e) => setAiInstructions(e.target.value)}
          className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary font-mono text-xs bg-input text-foreground"
          rows={15}
          placeholder="Load a campaign script using the buttons above or paste text/JSON here..."
          required
        />
      </div>

      <IncomingCallInterface
        audioMode={audioMode}
        isRecording={isRecording}
        isProcessing={isProcessing}
        stopLocalTest={stopLocalTest}
        handleLocalTest={handleLocalTest}
        fromPhoneNumber={fromPhoneNumber}
        incomingCallsEnabled={incomingCallsEnabled}
        aiInstructions={aiInstructions}
      />

      <CallStatusSection
        latestSummary={latestSummary}
        callStatus={callStatus}
        lastError={lastError}
        audioMode={audioMode}
        audioStatus={audioStatus}
        aiInitTimer={aiInitTimer}
        isRecording={isRecording}
        isConnected={isConnected}
      />
    </form>
  );
}
