'use client';

import { useState, useEffect, useCallback } from 'react';
import { CheckCircleIcon, ExclamationTriangleIcon, XCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface HealthStatus {
  success: boolean;
  timestamp: string;
  providerHealth: {
    gemini: {
      status: string;
      model: string;
      voice: string;
    };
  };
  connectionHealth: {
    totalConnections: number;
    activeConnections: number;
    failedConnections: number;
    recoveredConnections: number;
    lastHealthCheck: number;
    connectionStates: Array<{
      callSid: string;
      state: string;
      timestamp: number;
      consecutiveFailures: number;
    }>;
  };
  contextManager: {
    totalContexts: number;
    activeRecoveries: number;
  };
  activeConnections: number;
}

interface ConnectionMetrics {
  success: boolean;
  timestamp: string;
  metrics: {
    connections: Record<string, unknown>;
    audioQuality: {
      peakLevel: number;
      averageLevel: number;
      silencePercentage: number;
      clippingPercentage: number;
    };
    uptime: number;
    memory: {
      rss: number;
      heapUsed: number;
      heapTotal: number;
      external: number;
    };
  };
}

export default function ProviderHealthMonitor() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [connectionMetrics, setConnectionMetrics] = useState<ConnectionMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchHealthStatus = async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('/api/provider-health', {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      setHealthStatus(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching health status:', err);
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          setError('Request timeout - health check took too long');
        } else {
          setError(err.message);
        }
      } else {
        setError('Unknown error occurred');
      }
    }
  };

  const fetchConnectionMetrics = async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('/api/connection-metrics', {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      setConnectionMetrics(data);
    } catch (err) {
      console.error('Error fetching connection metrics:', err);
      // Don't set error state for metrics as it's less critical than health status
    }
  };

  const refreshData = useCallback(async () => {
    setIsLoading(true);
    await Promise.all([fetchHealthStatus(), fetchConnectionMetrics()]);
    setIsLoading(false);
  }, []);

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshData();
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, refreshData]);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'connected':
      case 'healthy':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'disconnected':
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'recovering':
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-500" />;
    }
  };



  if (isLoading && !healthStatus) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-center">
          <ArrowPathIcon className="h-6 w-6 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600 dark:text-gray-300">Loading health status...</span>
        </div>
      </div>
    );
  }

  if (error && !healthStatus) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Provider Health Monitor</h3>
          <button
            onClick={refreshData}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowPathIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="text-red-600 dark:text-red-400">
          <XCircleIcon className="h-5 w-5 inline mr-2" />
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Provider Health Monitor</h3>
        <div className="flex items-center space-x-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600 dark:text-gray-300">Auto-refresh</span>
          </label>
          <button
            onClick={refreshData}
            disabled={isLoading}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {healthStatus && (
        <div className="space-y-6">
          {/* Provider Status */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">Provider Status</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Gemini API</span>
                  {getStatusIcon(healthStatus.providerHealth?.gemini?.status || 'unknown')}
                </div>
                <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                  <div>Model: {healthStatus.providerHealth?.gemini?.model || 'N/A'}</div>
                  <div>Endpoint: Live API WebSocket</div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Active Connections</span>
                  {getStatusIcon(healthStatus.activeConnections > 0 ? 'connected' : 'disconnected')}
                </div>
                <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                  <div>Count: {healthStatus.activeConnections || 0}</div>
                  <div>Contexts: {healthStatus.contextManager?.totalContexts || 0}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Connection Health */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">Connection Health</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {healthStatus.connectionHealth?.totalConnections || 0}
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400">Total Connections</div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {healthStatus.connectionHealth?.activeConnections || 0}
                </div>
                <div className="text-xs text-green-600 dark:text-green-400">Active Connections</div>
              </div>
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {healthStatus.connectionHealth?.failedConnections || 0}
                </div>
                <div className="text-xs text-red-600 dark:text-red-400">Failed Connections</div>
              </div>
            </div>
          </div>

          {/* Connection Metrics */}
          {connectionMetrics && (
            <div>
              <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">Connection Metrics</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Audio Quality</h5>
                  <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                    <div>Peak Level: {connectionMetrics.metrics?.audioQuality?.peakLevel?.toFixed(1) || 'N/A'}</div>
                    <div>Average Level: {connectionMetrics.metrics?.audioQuality?.averageLevel?.toFixed(1) || 'N/A'}</div>
                    <div>Silence: {connectionMetrics.metrics?.audioQuality?.silencePercentage?.toFixed(1) || 'N/A'}%</div>
                    <div>Clipping: {connectionMetrics.metrics?.audioQuality?.clippingPercentage?.toFixed(1) || 'N/A'}%</div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Audio Quality</h5>
                  <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                    <div>Peak Level: {connectionMetrics.metrics?.audioQuality?.peakLevel || 'N/A'}</div>
                    <div>Average Level: {connectionMetrics.metrics?.audioQuality?.averageLevel || 'N/A'}</div>
                    <div>Silence: {connectionMetrics.metrics?.audioQuality?.silencePercentage || 'N/A'}%</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Last updated: {new Date(healthStatus.timestamp || Date.now()).toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
}
