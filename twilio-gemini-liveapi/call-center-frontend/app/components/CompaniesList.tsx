import { TrashIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

interface Company {
  name: string;
  phone: string;
  status?: 'pending' | 'in-progress' | 'polling-results' | 'completed' | 'failed';
  callSid?: string;
}

interface CompaniesListProps {
  companies: Company[];
  audioMode: 'twilio' | 'local';
  isRecording: boolean;
  handleLocalTest: (company: Company, index: number) => void;
  stopLocalTest: (index: number) => void;
  handleSingleCall: (company: Company, index: number) => void;
  handleStopCall: (callSid: string, index: number) => void;
  updateCompanyName: (index: number, value: string) => void;
  updateCompanyPhone: (index: number, value: string) => void;
  removeCompany: (index: number) => void;
  isCallButtonDisabled: (company: Company) => boolean;
}

export default function CompaniesList({
  companies,
  audioMode,
  isRecording,
  handleLocalTest,
  stopLocalTest,
  handleSingleCall,
  handleStopCall,
  updateCompanyName,
  updateCompanyPhone,
  removeCompany,
  isCallButtonDisabled
}: CompaniesListProps) {
  return (
    <>
      {companies.map((company, index) => (
        <div key={index} className="flex flex-col sm:flex-row gap-4 items-start border border-border rounded-lg p-4 bg-card">
          <div className="flex-1 w-full sm:w-1/3">
            <label htmlFor={`companyName-${index}`} className="sr-only">Target Name</label>
            <input
              id={`companyName-${index}`}
              type="text"
              value={company.name}
              onChange={(e) => updateCompanyName(index, e.target.value)}
              placeholder="Target Name"
              className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary bg-input text-foreground"
              required
            />
          </div>
          <div className="flex-1 w-full sm:w-1/3">
            <label htmlFor={`companyPhone-${index}`} className="sr-only">Phone Number (To Call)</label>
            <input
              id={`companyPhone-${index}`}
              type="tel"
              value={company.phone}
              onChange={(e) => updateCompanyPhone(index, e.target.value)}
              placeholder="Phone number (To Call) - Use international format: +1234567890"
              className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary bg-input text-foreground"
              required
            />
          </div>
          <div className="flex-1 w-full sm:w-1/3 flex justify-center sm:justify-end items-center gap-2">
            {audioMode === 'local' && isRecording && company.status === 'in-progress' ? (
              <button
                type="button"
                onClick={() => stopLocalTest(index)}
                className="bg-red-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Stop
              </button>
            ) : audioMode === 'twilio' && company.status === 'in-progress' && company.callSid ? (
              <button
                type="button"
                onClick={() => handleStopCall(company.callSid!, index)}
                className="bg-red-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Stop Call
              </button>
            ) : (
              <button
                type="button"
                onClick={() => audioMode === 'local' ? handleLocalTest(company, index) : handleSingleCall(company, index)}
                disabled={isCallButtonDisabled(company)}
                className="bg-green-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {audioMode === 'local' ? <>Test</> : <>Call</>}
              </button>
            )}
            <button
              type="button"
              onClick={() => removeCompany(index)}
              className="bg-red-100 text-red-600 p-2 rounded-md shadow-sm hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              aria-label="Remove company"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
          <div className="w-full sm:w-auto text-sm text-center sm:text-left mt-2 sm:mt-0 text-gray-600">
            {company.status && (
              <div>
                Status: <span className={cn('font-medium px-1.5 py-0.5 rounded',
                  company.status === 'completed' ? 'bg-green-100 text-green-800' :
                  company.status === 'failed' ? 'bg-red-100 text-red-800' :
                  company.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                  company.status === 'polling-results' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800')}
                >
                  {company.status.replace('-', ' ')}
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </>
  );
}
