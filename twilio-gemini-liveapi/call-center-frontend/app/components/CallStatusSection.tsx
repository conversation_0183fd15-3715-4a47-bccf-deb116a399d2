import { CallResult } from './CallResult';
import { cn } from '../../lib/utils';

interface DisplayCallResult {
  call_summary: string;
  customer_sentiment: string;
  callSid?: string;
  targetName?: string;
  targetPhoneNumber?: string;
  status?: string;
  timestamp?: string;
  recordingUrl?: string;
  recordingTimestamp?: string;
  duration?: string;
  callQuality?: string;
}

interface CallStatusSectionProps {
  latestSummary: DisplayCallResult | null;
  callStatus: string;
  lastError: string | null;
  audioMode: 'twilio' | 'local';
  audioStatus: string;
  aiInitTimer: number;
  isRecording: boolean;
  isConnected: boolean;
}

export default function CallStatusSection({
  latestSummary,
  callStatus,
  lastError,
  audioMode,
  audioStatus,
  aiInitTimer,
  isRecording,
  isConnected
}: CallStatusSectionProps) {
  return (
    <>
      {latestSummary && (
        <div className="mt-8 p-4 border border-border rounded-lg bg-card">
          <h3 className="text-lg font-semibold mb-4 text-foreground">Latest Call Results</h3>
          <CallResult result={latestSummary} />
        </div>
      )}
      <div className="mt-4 text-center text-sm text-gray-600">
        {callStatus !== 'idle' && (
          <div>
            Overall Status:{' '}
            <span className={cn('font-medium px-1.5 py-0.5 rounded',
              callStatus === 'completed' ? 'bg-green-100 text-green-800' :
              callStatus === 'failed' ? 'bg-red-100 text-red-800' :
              callStatus === 'in-progress' || callStatus === 'initiating-call' ? 'bg-blue-100 text-blue-800' :
              callStatus === 'polling-results' ? 'bg-yellow-100 text-yellow-800' :
              callStatus === 'loading-script' ? 'bg-purple-100 text-purple-800' :
              'bg-gray-100 text-gray-800')}
            >
              {callStatus.replace('-', ' ')}
            </span>
          </div>
        )}
        {lastError && <div className="mt-2 text-red-600">{lastError}</div>}
      </div>
      {audioMode === 'local' && (isRecording || isConnected || audioStatus !== 'Ready to start local testing') && (
        <div className="mt-4 text-center space-y-2">
          <span className={cn('inline-block px-3 py-1 rounded-full text-sm font-medium',
            isConnected ? 'bg-green-100 text-green-800' :
            isRecording ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800')}
          >
            {audioStatus}
          </span>
          {aiInitTimer > 0 && (
            <div className="mt-2">
              <div className={cn('inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold',
                aiInitTimer <= 3 ? 'bg-red-100 text-red-800' :
                aiInitTimer <= 5 ? 'bg-yellow-100 text-yellow-800' :
                'bg-blue-100 text-blue-800')}
              >
                {aiInitTimer}
              </div>
              <p className="text-sm text-gray-600 mt-1">AI initialization countdown</p>
            </div>
          )}
        </div>
      )}
    </>
  );
}
