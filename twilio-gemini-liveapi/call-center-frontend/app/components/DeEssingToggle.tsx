'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { cn } from '../../lib/utils';
import { authenticatedFetch } from '../utils/auth';

interface DeEssingToggleProps {
  className?: string;
}

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'https://gemini-api.verduona.com';

export default function DeEssingToggle({ className }: DeEssingToggleProps) {
  const [isEnabled, setIsEnabled] = useState(true); // Default enabled
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load current de-essing status on component mount
  useEffect(() => {
    const loadDeEssingStatus = async () => {
      try {
        const response = await authenticatedFetch(`${BACKEND_URL}/audio-settings`);
        if (response.ok) {
          const data = await response.json();
          setIsEnabled(data.deEssingEnabled ?? true);
        } else {
          console.warn('Failed to load de-essing status, using default');
        }
      } catch (error) {
        console.error('Error loading de-essing status:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    loadDeEssingStatus();
  }, []);

  const toggleDeEssing = async () => {
    if (isLoading) return;

    setIsLoading(true);
    const newState = !isEnabled;

    try {
      const response = await authenticatedFetch(`${BACKEND_URL}/audio-settings`, {
        method: 'POST',
        body: JSON.stringify({
          deEssingEnabled: newState,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setIsEnabled(data.deEssingEnabled);
        toast.success(
          `De-essing ${data.deEssingEnabled ? 'enabled' : 'disabled'}`,
          {
            icon: data.deEssingEnabled ? '🎛️' : '🔇',
            duration: 2000,
          }
        );
      } else {
        throw new Error('Failed to update de-essing setting');
      }
    } catch (error) {
      console.error('Error toggling de-essing:', error);
      toast.error('Failed to update de-essing setting');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isInitialized) {
    return (
      <div className={cn("flex items-center gap-3 p-3 bg-muted rounded-lg opacity-50", className)}>
        <span className="text-sm text-muted-foreground">Loading audio settings...</span>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-3 p-3 bg-muted rounded-lg", className)}>
      <div className="flex items-center gap-2">
        <span className="text-lg">🎛️</span>
        <label htmlFor="deessing-toggle" className="text-sm font-medium text-foreground">
          De-essing Filter
        </label>
      </div>
      
      <button
        id="deessing-toggle"
        onClick={toggleDeEssing}
        disabled={isLoading}
        className={cn(
          "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          isEnabled
            ? "bg-primary"
            : "bg-muted-foreground/20"
        )}
      >
        <span
          className={cn(
            "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
            "shadow-sm",
            isEnabled ? "translate-x-6" : "translate-x-1"
          )}
        />
      </button>
      
      <span className={cn(
        "text-sm font-medium min-w-[2rem]",
        isEnabled ? "text-green-600 dark:text-green-400" : "text-muted-foreground"
      )}>
        {isLoading ? "..." : (isEnabled ? "ON" : "OFF")}
      </span>
      
      <div className="text-xs text-muted-foreground ml-2">
        {isEnabled ? "Reduces harsh sibilants" : "No sibilant filtering"}
      </div>
    </div>
  );
}
