import { DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { PlusIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import DeEssingToggle from './DeEssingToggle';
import CompaniesList from './CompaniesList';
import CallStatusSection from './CallStatusSection';
import { CallResult } from './CallResult';

interface Company {
  name: string;
  phone: string;
  status?: 'pending' | 'in-progress' | 'polling-results' | 'completed' | 'failed';
  callSid?: string;
}

interface OutboundFormProps {
  AVAILABLE_COUNTRIES: { code: string; name: string; flag: string; phoneNumber: string }[];
  AVAILABLE_LANGUAGES: { code: string; name: string }[];
  availableModels: Record<string, unknown>;
  availableVoices: string[];
  voiceDetails: Record<string, any>;
  voiceDescriptions: Record<string, string>;
  selectedCountry: string;
  setSelectedCountry: (val: string) => void;
  selectedModel: string;
  setSelectedModel: (val: string) => void;
  selectedVoice: string;
  setSelectedVoice: (val: string) => void;
  selectedLanguage: string;
  setSelectedLanguage: (val: string) => void;
  aiInstructions: string;
  setAiInstructions: (val: string) => void;
  loadCampaignScript: (id: number) => void;
  audioMode: 'twilio' | 'local';
  companies: Company[];
  updateCompanyName: (index: number, value: string) => void;
  updateCompanyPhone: (index: number, value: string) => void;
  removeCompany: (index: number) => void;
  addCompany: () => void;
  handleLocalTest: (company: Company, index: number) => void;
  stopLocalTest: (index: number) => void;
  handleSingleCall: (company: Company, index: number) => void;
  handleStopCall: (callSid: string, index: number) => void;
  isCallButtonDisabled: (company: Company) => boolean;
  latestSummary: any | null;
  callStatus: string;
  lastError: string | null;
  audioStatus: string;
  aiInitTimer: number;
  isRecording: boolean;
  isConnected: boolean;
  isProcessing: boolean;
  isLoadingScript: boolean;
}

export default function OutboundForm({
  AVAILABLE_COUNTRIES,
  AVAILABLE_LANGUAGES,
  availableModels,
  availableVoices,
  voiceDetails,
  voiceDescriptions,
  selectedCountry,
  setSelectedCountry,
  selectedModel,
  setSelectedModel,
  selectedVoice,
  setSelectedVoice,
  selectedLanguage,
  setSelectedLanguage,
  aiInstructions,
  setAiInstructions,
  loadCampaignScript,
  audioMode,
  companies,
  updateCompanyName,
  updateCompanyPhone,
  removeCompany,
  addCompany,
  handleLocalTest,
  stopLocalTest,
  handleSingleCall,
  handleStopCall,
  isCallButtonDisabled,
  latestSummary,
  callStatus,
  lastError,
  audioStatus,
  aiInitTimer,
  isRecording,
  isConnected,
  isProcessing,
  isLoadingScript
}: OutboundFormProps) {
  return (
    <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
      {/* Country Selection Dropdown */}
      <div>
        <label htmlFor="countrySelect" className="block text-sm font-medium mb-2 text-foreground">
          Select Country / Twilio Number
        </label>
        <select
          id="countrySelect"
          value={selectedCountry}
          onChange={(e) => setSelectedCountry(e.target.value)}
          className={cn(
            "w-full p-2 border border-border rounded-md shadow-sm",
            "focus:ring-primary focus:border-primary bg-input text-foreground",
            "transition-colors duration-200"
          )}
        >
          {AVAILABLE_COUNTRIES.map((country) => (
            <option key={country.code} value={country.code}>
              {country.flag} {country.name} ({country.phoneNumber})
            </option>
          ))}
        </select>
      </div>

      {/* Model Selection Dropdown */}
      <div>
        <label htmlFor="modelSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select AI Model
        </label>
        <select
          id="modelSelect"
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          className={cn(
            "w-full p-2 border border-border rounded-md shadow-sm",
            "focus:ring-primary focus:border-primary bg-input text-foreground",
            "transition-colors duration-200"
          )}
        >
          {Object.entries(availableModels).map(([modelId, modelInfo]) => (
            <option key={modelId} value={modelId}>
              {typeof modelInfo === 'object' && modelInfo !== null && 'name' in modelInfo ? (modelInfo as { name: string }).name : String(modelInfo)}
            </option>
          ))}
        </select>
      </div>

      {/* Voice Selection Dropdown */}
      <div>
        <label htmlFor="voiceSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select AI Voice Sound
        </label>
        <select
          id="voiceSelect"
          value={selectedVoice}
          onChange={(e) => setSelectedVoice(e.target.value)}
          className={cn(
            "w-full p-2 border border-border rounded-md shadow-sm",
            "focus:ring-primary focus:border-primary bg-input text-foreground",
            "transition-colors duration-200"
          )}
        >
          {availableVoices.map((voice) => {
            const details = voiceDetails[voice] as {
              gender?: string;
              characteristics?: string;
              description?: string;
              useCase?: string;
            } | undefined;

            const envDescription = voiceDescriptions[voice];
            const displayText = envDescription || (details
              ? `${voice} - ${details.gender || 'Unknown'} (${details.characteristics || 'No description'})`
              : voice.charAt(0).toUpperCase() + voice.slice(1));

            return (
              <option key={voice} value={voice} className="bg-input text-foreground">
                {displayText}
              </option>
            );
          })}
        </select>

      </div>

      {/* Language Selection Dropdown */}
      <div>
        <label htmlFor="languageSelect" className="block text-sm font-medium mb-2 text-foreground">
          Select Output Language
        </label>
        <select
          id="languageSelect"
          value={selectedLanguage}
          onChange={(e) => setSelectedLanguage(e.target.value)}
          className={cn(
            "w-full p-2 border border-border rounded-md shadow-sm",
            "focus:ring-primary focus:border-primary bg-input text-foreground",
            "transition-colors duration-200"
          )}
        >
          {AVAILABLE_LANGUAGES.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.name}
            </option>
          ))}
        </select>
      </div>

      {/* Campaign Load Buttons - Available Campaigns Only */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-foreground">
          Load Outbound Campaign Script (Available Campaigns)
        </label>
        <div className="grid grid-cols-6 gap-2 text-xs font-semibold text-muted-foreground text-center">
          <div className="p-2 bg-accent/20 rounded">English Insurance</div>
          <div className="p-2 bg-accent/20 rounded">English Fundraising</div>
          <div className="p-2 bg-accent/20 rounded">Spanish Insurance</div>
          <div className="p-2 bg-accent/20 rounded">Spanish Fundraising</div>
          <div className="p-2 bg-accent/20 rounded">Czech Insurance</div>
          <div className="p-2 bg-accent/20 rounded">Czech Fundraising</div>
        </div>
        <div className="grid grid-cols-6 gap-2">
          {[1,2,3,4,5,6].map((id) => (
            <button
              key={id}
              type="button"
              onClick={() => loadCampaignScript(id)}
              className="bg-gray-100 text-gray-800 px-3 py-2 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
            >
              <DocumentArrowDownIcon className="h-4 w-4 inline mr-1" />
              {id} (Out)
            </button>
          ))}
        </div>
      </div>

      {/* Task Description Input */}
      <div>
        <label htmlFor="aiInstructionsDescription" className="block text-sm font-medium mb-2 text-foreground">
          Campaign Script / AI Instructions (Text Format - JSON supported)
        </label>
        <textarea
          id="aiInstructionsDescription"
          value={aiInstructions}
          onChange={(e) => setAiInstructions(e.target.value)}
          className="w-full p-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary font-mono text-xs bg-input text-foreground"
          rows={15}
          placeholder="Load a campaign script using the buttons above or paste text/JSON here..."
          required
        />
      </div>

      {/* Call Interface */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold mb-2 text-foreground">
          {audioMode === 'local' ? 'Local Audio Testing (Browser)' : 'Phone Numbers to Call'}
        </h3>
        <CompaniesList
          companies={companies}
          audioMode={audioMode}
          isRecording={isRecording}
          handleLocalTest={handleLocalTest}
          stopLocalTest={stopLocalTest}
          handleSingleCall={handleSingleCall}
          handleStopCall={handleStopCall}
          updateCompanyName={updateCompanyName}
          updateCompanyPhone={updateCompanyPhone}
          removeCompany={removeCompany}
          isCallButtonDisabled={isCallButtonDisabled}
        />
        <button
          type="button"
          onClick={addCompany}
          disabled={companies.length >= 12}
          className="mt-2 inline-flex items-center px-3 py-2 border border-border shadow-sm text-sm leading-4 font-medium rounded-md text-foreground bg-card hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <PlusIcon className="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
          Add Phone Number
        </button>
      </div>

      <CallStatusSection
        latestSummary={latestSummary}
        callStatus={callStatus}
        lastError={lastError}
        audioMode={audioMode}
        audioStatus={audioStatus}
        aiInitTimer={aiInitTimer}
        isRecording={isRecording}
        isConnected={isConnected}
      />
      <DeEssingToggle />
    </form>
  );
}
