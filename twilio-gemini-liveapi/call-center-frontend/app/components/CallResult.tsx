'use client';

// Enhanced call result structure with more useful information
interface DisplayCallResult {
  call_summary: string;
  customer_sentiment: string;
  callSid?: string;
  targetName?: string;
  targetPhoneNumber?: string;
  status?: string;
  timestamp?: string;
  recordingUrl?: string;
  recordingTimestamp?: string;
  duration?: string;
  callQuality?: string;
}

interface Props {
  result: DisplayCallResult;
}

export function CallResult({ result }: Props) {
  // Calculate call duration if timestamps are available
  const calculateDuration = () => {
    if (result.timestamp && result.recordingTimestamp) {
      const start = new Date(result.recordingTimestamp).getTime();
      const end = new Date(result.timestamp).getTime();
      const durationMs = end - start;
      const seconds = Math.floor(durationMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
    }
    return result.duration || 'Unknown';
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp).toLocaleString();
  };

  // Get status color
  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'no-answer': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm space-y-4">
      {/* Header with Call ID and Status */}
      <div className="flex justify-between items-start">
        <div>
          <h4 className="font-medium text-gray-900">
            Call {result.callSid ? `#${result.callSid.slice(-8)}` : 'Unknown'}
          </h4>
          {result.targetName && (
            <p className="text-sm text-gray-600">
              To: {result.targetName} ({result.targetPhoneNumber})
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
            {result.status || 'Unknown'}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            result.customer_sentiment === 'positive' ? 'bg-green-100 text-green-800' :
            result.customer_sentiment === 'negative' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {result.customer_sentiment || 'neutral'} sentiment
          </span>
        </div>
      </div>

      {/* Call Details Grid */}
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="font-medium text-gray-500">Duration:</span>
          <span className="ml-2 text-gray-900">{calculateDuration()}</span>
        </div>
        <div>
          <span className="font-medium text-gray-500">Time:</span>
          <span className="ml-2 text-gray-900">{formatTimestamp(result.timestamp)}</span>
        </div>
        {result.callQuality && (
          <div>
            <span className="font-medium text-gray-500">Quality:</span>
            <span className="ml-2 text-gray-900">{result.callQuality}</span>
          </div>
        )}
        {result.recordingUrl && (
          <div>
            <span className="font-medium text-gray-500">Recording:</span>
            <a
              href={result.recordingUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-2 text-blue-600 hover:text-blue-800 underline"
            >
              🎵 Play Recording
            </a>
          </div>
        )}
      </div>

      {/* Call Summary */}
      <div>
        <dt className="text-sm font-medium text-gray-500 mb-2">Call Summary / Report</dt>
        <dd className="text-sm text-gray-800 whitespace-pre-wrap bg-slate-50 p-3 rounded border border-slate-200">
          {result.call_summary || "Summary not yet generated or call ended prematurely."}
        </dd>
      </div>
    </div>
  );
}