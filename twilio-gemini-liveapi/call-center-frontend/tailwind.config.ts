import type { Config } from "tailwindcss";

export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Semantic color tokens from Verduona green theme
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))'
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          foreground: 'hsl(var(--warning-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        // Verduona brand colors for backward compatibility
        brand: {
          50: '#f0fdf4',   // Very light green (almost white)
          100: '#dcfce7',  // Light green
          200: '#bbf7d0',  // Soft green
          300: '#86efac',  // Light-medium green
          400: '#4ade80',  // Medium green
          500: '#22c55e',  // Main brand green
          600: '#16a34a',  // Dark green (primary)
          700: '#15803d',  // Darker green
          800: '#166534',  // Very dark green
          900: '#14532d',  // Deepest green
        },
        // Verde accent colors for highlights
        verde: {
          50: '#f0fdf0',   // Lightest verde
          100: '#dcfcd4',  // Light verde
          200: '#bbf7a0',  // Soft verde accent
          300: '#86ef87',  // Medium verde
          400: '#4ade50',  // Bright verde
          500: '#22c532',  // Main verde accent
          600: '#16a316',  // Dark verde
          700: '#15801a',  // Darker verde
          800: '#166520',  // Very dark verde  
          900: '#145326',  // Deepest verde
        },
        // Support colors
        forest: {
          50: '#f6fdf6',
          100: '#e8f9e8',
          200: '#d1f2d1',
          300: '#a7e7a7',
          400: '#6dd46d',
          500: '#4ade50',
          600: '#22c532',
          700: '#1ea824',
          800: '#1e8920',
          900: '#1a6f1c',
        }
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      }
    },
  },
  plugins: [],
} satisfies Config;
