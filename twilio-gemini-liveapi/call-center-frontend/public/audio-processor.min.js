function structuredLog(level, message, data = {}) { const entry = { t: Date.now(), level, message, ...data, }; const json = JSON.stringify(entry); if (level === 'error' || level === 'warn') { console.error(json); } else { console.info(json); } } class AudioProcessor extends AudioWorkletProcessor { constructor() { super(); this.bufferSize = 256; this.buffer = new Float32Array(this.bufferSize); this.bufferIndex = 0; } process(inputs, outputs, parameters) { const input = inputs[0]; if (input && input.length > 0) { const inputChannel = input[0]; if (this.bufferIndex === 0 && this.frameCount % 100 === 0) { structuredLog('info', '🎙️ AudioWorklet processing audio', { frames: this.frameCount }); } this.frameCount = (this.frameCount || 0) + 1; for (let i = 0; i < inputChannel.length; i++) { this.buffer[this.bufferIndex] = inputChannel[i]; this.bufferIndex++; if (this.bufferIndex >= this.bufferSize) { const int16Array = new Int16Array(this.bufferSize); for (let j = 0; j < this.bufferSize; j++) { int16Array[j] = Math.max(-32768, Math.min(32767, this.buffer[j] * 32768)); } const uint8Array = new Uint8Array(int16Array.buffer); if (this.sendCount % 50 === 0) { structuredLog('info', '📤 AudioWorklet sent audio packets', { packets: this.sendCount }); } this.sendCount = (this.sendCount || 0) + 1; this.port.postMessage({ type: 'audioData', data: uint8Array }); this.bufferIndex = 0; } } } else { if (this.bufferIndex === 0) { structuredLog('warn', '⚠️ AudioWorklet: No input data available'); } } return true; } } registerProcessor('audio-processor', AudioProcessor);