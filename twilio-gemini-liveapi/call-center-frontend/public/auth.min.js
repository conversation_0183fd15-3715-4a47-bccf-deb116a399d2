function initAuth() { const token = getTokenFromURL(); const validation = validateToken(token); if (!validation.valid) { structuredLog('error', 'Authentication failed', { error: validation.error }); showAuthError(validation.error); return false; } structuredLog('info', 'Authentication successful', { user: validation.user.email }); window.currentUser = validation.user; document.documentElement.style.display = ''; return true; } if (document.readyState === 'loading') { document.addEventListener('DOMContentLoaded', initAuth); } else { initAuth(); } window.initAuth = initAuth; window.validateToken = validateToken; window.getTokenFromURL = getTokenFromURL;