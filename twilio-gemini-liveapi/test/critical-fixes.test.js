import './helpers/env.js';
import { test } from 'node:test';
import assert from 'node:assert';
import { CampaignConfigManager } from '../src/config/campaign-config.ts';
import { forwardAudio, initializeAudioForwarding } from '../src/audio/audio-forwarding.ts';
import { standardizeConnectionData, addToBoundedArray } from '../src/utils/websocket-utils.ts';

test('Critical Fixes Verification', async (t) => {
    
    await t.test('Path Traversal Security Fix', () => {
        const manager = new CampaignConfigManager();
        
        // Test that path traversal attempts are blocked
        const maliciousFilenames = [
            '../../../etc/passwd.json',
            '..\\..\\windows\\system32\\config.json',
            'campaign1/../../../secret.json',
            'campaign1.json/../../../secret.json'
        ];
        
        for (const filename of maliciousFilenames) {
            const result = manager.loadCampaignFromFile(filename);
            assert.strictEqual(result, null, `Path traversal attempt should be blocked: ${filename}`);
        }
        
        // Test that valid filenames work
        const validFilename = 'campaign1.json';
        // This will return null because file doesn't exist, but it should not throw security error
        const validResult = manager.loadCampaignFromFile(validFilename);
        // We expect null because the file doesn't exist, but no security error should be thrown
        assert.strictEqual(validResult, null);
    });

    await t.test('Cache Invalidation Functions', () => {
        const manager = new CampaignConfigManager();
        
        // Test cache invalidation methods exist and work
        assert.ok(typeof manager.invalidateCache === 'function');
        assert.ok(typeof manager.invalidateCampaignCache === 'function');
        
        // Test cache stats
        const stats = manager.getCacheStats();
        assert.ok(typeof stats === 'object');
        assert.ok(typeof stats.size === 'number');
        assert.ok(Array.isArray(stats.keys));
        assert.ok(typeof stats.timeout === 'number');
        assert.ok(typeof stats.maxSize === 'number');
    });

    await t.test('Audio Forwarding Functions', () => {
        // Test that audio forwarding functions exist
        assert.ok(typeof forwardAudio === 'function');
        assert.ok(typeof initializeAudioForwarding === 'function');
        
        // Test initialization
        const connectionData = {
            sessionType: 'twilio_call',
            isTwilioCall: true
        };
        
        initializeAudioForwarding('test-session', connectionData, 'twilio_call');
        
        assert.strictEqual(connectionData.audioForwardingEnabled, true);
        assert.strictEqual(connectionData.sequenceNumber, 0);
        assert.strictEqual(connectionData.lastAudioSent, 0);
    });

    await t.test('WebSocket Utilities', () => {
        // Test standardizeConnectionData
        const ws = { readyState: 1 }; // Mock WebSocket
        const connectionData = {};
        
        const standardized = standardizeConnectionData(connectionData, ws, 'twilio_call');
        
        assert.strictEqual(standardized.ws, ws);
        assert.strictEqual(standardized.twilioWs, ws);
        assert.ok(Array.isArray(standardized.conversationLog));
        assert.ok(Array.isArray(standardized.fullTranscript));
        assert.ok(Array.isArray(standardized.speechTranscript));
        assert.strictEqual(standardized.maxConversationLogSize, 500);
        assert.strictEqual(standardized.maxTranscriptSize, 1000);
        assert.strictEqual(standardized.maxSpeechTranscriptSize, 1000);
    });

    await t.test('Bounded Array Memory Management', () => {
        // Test addToBoundedArray function
        let testArray = [];
        
        // Add items within limit
        for (let i = 0; i < 5; i++) {
            testArray = addToBoundedArray(testArray, `item${i}`, 10);
        }
        assert.strictEqual(testArray.length, 5);
        
        // Add items beyond limit
        for (let i = 5; i < 15; i++) {
            testArray = addToBoundedArray(testArray, `item${i}`, 10);
        }
        assert.strictEqual(testArray.length, 10);
        
        // Check that oldest items were removed
        assert.strictEqual(testArray[0], 'item5'); // First item should be item5, not item0
        assert.strictEqual(testArray[9], 'item14'); // Last item should be item14
    });

    await t.test('Session Type Detection Fix', () => {
        // Test that isIncomingCall is properly detected
        const testCases = [
            { query: { type: 'incoming' }, expected: true },
            { query: { flow: 'inbound' }, expected: true },
            { sessionConfig: { isIncomingCall: true }, expected: true },
            { sessionConfig: { scriptType: 'incoming' }, expected: true },
            { query: { type: 'outbound' }, expected: false },
            { query: {}, expected: false }
        ];
        
        for (const testCase of testCases) {
            const connection = { query: testCase.query || {} };
            const sessionConfig = testCase.sessionConfig || {};
            
            const isIncomingCall = sessionConfig.isIncomingCall || 
                                  connection.query?.type === 'incoming' || 
                                  connection.query?.flow === 'inbound' ||
                                  sessionConfig.scriptType === 'incoming';
            
            assert.strictEqual(isIncomingCall, testCase.expected, 
                `Failed for test case: ${JSON.stringify(testCase)}`);
        }
    });

    await t.test('Audio Field Name Standardization', () => {
        // Test that audio field names are properly standardized
        const testData1 = { audio: 'test-audio-data' };
        const testData2 = { audioData: 'test-audio-data' };
        const testData3 = { audio: 'primary', audioData: 'fallback' };
        
        // Test preference for 'audio' over 'audioData'
        assert.strictEqual(testData1.audio || testData1.audioData, 'test-audio-data');
        assert.strictEqual(testData2.audio || testData2.audioData, 'test-audio-data');
        assert.strictEqual(testData3.audio || testData3.audioData, 'primary'); // Should prefer 'audio'
    });

    await t.test('Sequence Number Initialization', () => {
        // Test that sequence numbers are properly initialized for Twilio calls
        const connectionData = {
            sessionType: 'twilio_call',
            isTwilioCall: true
        };

        initializeAudioForwarding('test-session', connectionData, 'twilio_call');

        assert.strictEqual(typeof connectionData.sequenceNumber, 'number');
        assert.strictEqual(connectionData.sequenceNumber, 0);

        // Test sequence number increment simulation
        connectionData.sequenceNumber++;
        assert.strictEqual(connectionData.sequenceNumber, 1);
    });

    await t.test('Audio Forwarding Integration', async () => {
        // Test that audio forwarding works with proper WebSocket connections
        const mockTwilioWs = {
            readyState: 1,
            send: function(data) {
                this.lastSent = JSON.parse(data);
            },
            lastSent: null
        };

        const mockLocalWs = {
            readyState: 1,
            send: function(data) {
                this.lastSent = JSON.parse(data);
            },
            lastSent: null
        };

        // Test Twilio audio forwarding
        const twilioConnectionData = {
            sessionType: 'twilio_call',
            isTwilioCall: true,
            ws: mockTwilioWs,
            twilioWs: mockTwilioWs,
            streamSid: 'test-stream-sid',
            sequenceNumber: 0
        };

        const mockAudioProcessor = {
            convertPCMToUlaw: (data) => 'converted-audio-data',
            fallbackPCMToUlaw: (data) => 'fallback-converted-audio-data'
        };

        const mockAudio = {
            data: 'base64-audio-data',
            mimeType: 'audio/pcm;rate=24000'
        };

        const success = await forwardAudio('test-session', mockAudio, twilioConnectionData, mockAudioProcessor);
        assert.strictEqual(success, true);
        assert.strictEqual(mockTwilioWs.lastSent.event, 'media');
        assert.strictEqual(mockTwilioWs.lastSent.streamSid, 'test-stream-sid');
        assert.strictEqual(mockTwilioWs.lastSent.sequenceNumber, '0');

        // Test local audio forwarding
        const localConnectionData = {
            sessionType: 'local_test',
            isTwilioCall: false,
            ws: mockLocalWs,
            localWs: mockLocalWs
        };

        const localSuccess = await forwardAudio('test-session', mockAudio, localConnectionData, mockAudioProcessor);
        assert.strictEqual(localSuccess, true);
        assert.strictEqual(mockLocalWs.lastSent.type, 'audio');
        assert.strictEqual(mockLocalWs.lastSent.audio, 'base64-audio-data');
        assert.strictEqual(mockLocalWs.lastSent.mimeType, 'audio/pcm;rate=24000');
    });
});

test('Security Validation', async (t) => {
    
    await t.test('Filename Validation Regex', () => {
        const validFilenames = [
            'campaign1.json',
            'campaign-test.json',
            'incoming-campaign1.json',
            'test123.json'
        ];
        
        const invalidFilenames = [
            'campaign1.txt',
            'campaign1.json.exe',
            'campaign1',
            '../campaign1.json',
            'campaign1.json/../test',
            'campaign1.json\0.txt',
            'campaign1.json;rm -rf /',
            'campaign1.json|cat /etc/passwd'
        ];
        
        const regex = /^[a-zA-Z0-9-]+\.json$/;
        
        for (const filename of validFilenames) {
            assert.ok(regex.test(filename), `Valid filename should pass: ${filename}`);
        }
        
        for (const filename of invalidFilenames) {
            assert.ok(!regex.test(filename), `Invalid filename should fail: ${filename}`);
        }
    });

    await t.test('Path Traversal Character Detection', () => {
        const maliciousFilenames = [
            'campaign1..json',
            'campaign1/.json',
            'campaign1\\.json',
            '../campaign1.json',
            'campaign1/../test.json'
        ];
        
        for (const filename of maliciousFilenames) {
            const hasTraversal = filename.includes('..') || 
                                filename.includes('/') || 
                                filename.includes('\\');
            assert.ok(hasTraversal, `Should detect traversal in: ${filename}`);
        }
        
        const safeFilename = 'campaign1.json';
        const isSafe = !safeFilename.includes('..') && 
                      !safeFilename.includes('/') && 
                      !safeFilename.includes('\\');
        assert.ok(isSafe, 'Safe filename should pass traversal check');
    });
});
