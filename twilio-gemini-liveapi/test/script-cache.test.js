import './helpers/env.js';
import { test } from 'node:test';
import assert from 'node:assert';
import { ScriptCache } from '../src/scripts/script-cache.ts';
import { CampaignConfigManager } from '../src/config/campaign-config.ts';
import sinon from 'sinon';
import * as supabase from '@supabase/supabase-js';

test('ScriptCache retrieves items within timeout', () => {
    const cache = new ScriptCache(50);
    cache.set('a', { foo: 'bar' });
    const item = cache.get('a');
    assert.strictEqual(item.foo, 'bar');
});

test('ScriptCache expires items after timeout', async () => {
    const cache = new ScriptCache(10);
    cache.set('b', { foo: 'bar' });
    await new Promise(r => setTimeout(r, 20));
    const item = cache.get('b');
    assert.strictEqual(item, null);
});

test('loadCampaignFromDatabase returns null without config', async () => {
    const manager = new CampaignConfigManager();
    const result = await manager['loadCampaignFromDatabase'](1, 'outbound');
    assert.strictEqual(result, null);
});

test('loadCampaignFromDatabase uses Supabase when configured', async () => {
    const manager = new CampaignConfigManager();
    process.env.SUPABASE_URL = 'http://example.com';
    process.env.SUPABASE_ANON_KEY = 'anon-key';

    const stub = sinon.stub(supabase, 'createClient').returns({
        from: () => ({
            select: () => ({
                eq: () => ({
                    eq: () => ({
                        single: async () => ({
                            data: {
                                id: 1,
                                type: 'outbound',
                                title: 'DB Campaign',
                                campaign: 'script',
                                agentPersona: { name: 'Agent' },
                                script: { start: [] }
                            },
                            error: null
                        })
                    })
                })
            })
        })
    });

    const result = await manager['loadCampaignFromDatabase'](1, 'outbound');

    assert.ok(result);
    if (result) {
        assert.strictEqual(result.title, 'DB Campaign');
    }

    stub.restore();
    delete process.env.SUPABASE_URL;
    delete process.env.SUPABASE_ANON_KEY;
});
