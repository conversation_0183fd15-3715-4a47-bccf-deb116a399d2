import './helpers/env.js';
import { describe, test, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert/strict';
import sinon from 'sinon';
import { SessionRecoveryManager } from '../src/session/recovery-manager.ts';


describe('SessionRecoveryManager', () => {
    let recoveryManager;
    let contextManager;
    let healthMonitor;
    let sessionManager;
    let activeConnections;

    beforeEach(() => {
        activeConnections = new Map();
        contextManager = {
            getSessionContext: sinon.stub(),
            canRecover: sinon.stub().returns(true),
            incrementRecoveryAttempt: sinon.stub().returns(1),
            getRecoveryMessage: sinon.stub().returns('resume'),
            contextStore: new Map()
        };
        healthMonitor = { trackConnection: sinon.stub() };
        sessionManager = { createGeminiSession: sinon.stub().resolves({ id: 'new-session' }) };

        recoveryManager = new SessionRecoveryManager(
            contextManager,
            healthMonitor,
            sessionManager
        );
    });

    afterEach(() => {
        // Clean up any scheduled retries or health checks
        if (recoveryManager) {
            recoveryManager.cleanup();
        }
        // Restore stubs
        sinon.restore();
    });

    test('should recreate session via SessionManager', async () => {
        const callSid = 'CA12345';
        const connectionData = {
            sessionId: callSid,
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isSessionActive: false
        };
        activeConnections.set(callSid, connectionData);

        const context = {
            sessionConfig: {
                aiInstructions: 'Hi',
                voice: 'TestVoice',
                model: 'test-model',
                isIncomingCall: false,
                scriptId: null,
                scriptType: 'outbound'
            },
            conversationState: { conversationLog: [], fullTranscript: [], speechTranscript: [] },
            connectionState: { lastActivity: Date.now(), sessionStartTime: Date.now(), keepAliveActive: false },
            recoveryInfo: {
                lastRecoveryTime: null,
                recoveryCount: 0,
                wasInterrupted: false,
                interruptionReason: null,
                interruptionTime: null,
                maxRecoveryAttempts: 3
            }
        };
        contextManager.getSessionContext.returns(context);

        const result = await recoveryManager.recoverSession(callSid, 'test', activeConnections);

        assert.equal(result, true);
        assert.ok(sessionManager.createGeminiSession.calledOnce);
        assert.deepEqual(sessionManager.createGeminiSession.firstCall.args, [callSid, context.sessionConfig, connectionData]);
        assert.equal(activeConnections.get(callSid).geminiSession.id, 'new-session');
        assert.ok(healthMonitor.trackConnection.calledWith(callSid, 'recovered'));
    });

    test('should handle session creation failure', { timeout: 5000 }, async () => {
        // Mock to ensure the context manager doesn't allow infinite retries
        let attempts = 0;
        contextManager.canRecover.callsFake(() => {
            attempts++;
            // Allow only 3 recovery attempts to prevent infinite loop
            return attempts <= 3;
        });
        
        sessionManager.createGeminiSession.rejects(new Error('fail'));
        const callSid = 'CA99999';
        const connectionData = {
            sessionId: callSid,
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isSessionActive: false
        };
        activeConnections.set(callSid, connectionData);

        const context = {
            sessionConfig: {
                aiInstructions: 'Hi',
                voice: 'TestVoice',
                model: 'test-model',
                isIncomingCall: false,
                scriptId: null,
                scriptType: 'outbound'
            },
            conversationState: { conversationLog: [], fullTranscript: [], speechTranscript: [] },
            connectionState: { lastActivity: Date.now(), sessionStartTime: Date.now(), keepAliveActive: false },
            recoveryInfo: {
                lastRecoveryTime: null,
                recoveryCount: 0,
                wasInterrupted: false,
                interruptionReason: null,
                interruptionTime: null,
                maxRecoveryAttempts: 3
            }
        };
        contextManager.getSessionContext.returns(context);

        const result = await recoveryManager.recoverSession(callSid, 'fail', activeConnections);

        assert.equal(result, false);
        // Recovery manager retries 3 times on failure
        assert.ok(sessionManager.createGeminiSession.calledThrice);
        assert.equal(activeConnections.get(callSid).geminiSession, undefined);
    });
});
