import './helpers/env.js';
import { test } from 'node:test';
import assert from 'node:assert';
import { forwardAudio, initializeAudioForwarding, cleanupAudioForwarding } from '../src/audio/audio-forwarding.ts';

// Mock WebSocket for testing
class MockWebSocket {
    constructor(readyState = 1) {
        this.readyState = readyState;
        this.sentMessages = [];
    }

    send(message) {
        this.sentMessages.push(message);
    }

    close() {
        this.readyState = 3;
    }
}

// Mock audio processor
class MockAudioProcessor {
    convertPCMToUlaw(data) {
        return 'mock-ulaw-data';
    }
}

test('Audio Forwarding Integration Tests', async (t) => {
    await t.test('Initialize audio forwarding for Twilio calls', () => {
        const connectionData = {};
        initializeAudioForwarding('test-session', connectionData, 'twilio_call');
        
        assert.strictEqual(connectionData.audioForwardingEnabled, true);
        assert.strictEqual(connectionData.sequenceNumber, 0);
        assert.strictEqual(connectionData.lastAudioSent, 0);
    });

    await t.test('Initialize audio forwarding for local testing', () => {
        const connectionData = {};
        initializeAudioForwarding('test-session', connectionData, 'local_test');
        
        assert.strictEqual(connectionData.audioForwardingEnabled, true);
        assert.strictEqual(connectionData.lastAudioSent, 0);
        assert.strictEqual(connectionData.sequenceNumber, undefined); // No sequence number for local
    });

    await t.test('Forward audio to Twilio WebSocket', async () => {
        const mockWs = new MockWebSocket();
        const connectionData = {
            twilioWs: mockWs,
            streamSid: 'test-stream-sid',
            sequenceNumber: 0,
            isTwilioCall: true,
            sessionType: 'twilio_call'
        };
        
        const audio = {
            data: 'test-audio-data',
            mimeType: 'audio/pcm'
        };
        
        const audioProcessor = new MockAudioProcessor();
        
        const result = await forwardAudio('test-session', audio, connectionData, audioProcessor);
        
        assert.strictEqual(result, true);
        assert.strictEqual(mockWs.sentMessages.length, 1);
        assert.strictEqual(connectionData.sequenceNumber, 1);
        
        const sentMessage = JSON.parse(mockWs.sentMessages[0]);
        assert.strictEqual(sentMessage.event, 'media');
        assert.strictEqual(sentMessage.streamSid, 'test-stream-sid');
        assert.strictEqual(sentMessage.sequenceNumber, '0');
    });

    await t.test('Forward audio to local WebSocket', async () => {
        const mockWs = new MockWebSocket();
        const connectionData = {
            localWs: mockWs,
            sessionType: 'local_test'
        };
        
        const audio = {
            data: 'test-audio-data',
            mimeType: 'audio/pcm'
        };
        
        const result = await forwardAudio('test-session', audio, connectionData);
        
        assert.strictEqual(result, true);
        assert.strictEqual(mockWs.sentMessages.length, 1);
        
        const sentMessage = JSON.parse(mockWs.sentMessages[0]);
        assert.strictEqual(sentMessage.type, 'audio');
        assert.strictEqual(sentMessage.audio, 'test-audio-data');
        assert.strictEqual(sentMessage.sessionId, 'test-session');
    });

    await t.test('Handle WebSocket not ready', async () => {
        const mockWs = new MockWebSocket(0); // CONNECTING state
        const connectionData = {
            twilioWs: mockWs,
            streamSid: 'test-stream-sid',
            isTwilioCall: true,
            sessionType: 'twilio_call'
        };
        
        const audio = {
            data: 'test-audio-data',
            mimeType: 'audio/pcm'
        };
        
        const audioProcessor = new MockAudioProcessor();
        
        const result = await forwardAudio('test-session', audio, connectionData, audioProcessor);
        
        assert.strictEqual(result, false);
        assert.strictEqual(mockWs.sentMessages.length, 0);
    });

    await t.test('Handle missing audio data', async () => {
        const mockWs = new MockWebSocket();
        const connectionData = {
            twilioWs: mockWs,
            streamSid: 'test-stream-sid',
            isTwilioCall: true,
            sessionType: 'twilio_call'
        };
        
        const audio = null;
        const audioProcessor = new MockAudioProcessor();
        
        const result = await forwardAudio('test-session', audio, connectionData, audioProcessor);
        
        assert.strictEqual(result, false);
        assert.strictEqual(mockWs.sentMessages.length, 0);
    });

    await t.test('Sequence number overflow protection', async () => {
        const mockWs = new MockWebSocket();
        const connectionData = {
            twilioWs: mockWs,
            streamSid: 'test-stream-sid',
            sequenceNumber: Number.MAX_SAFE_INTEGER - 1,
            isTwilioCall: true,
            sessionType: 'twilio_call'
        };
        
        const audio = {
            data: 'test-audio-data',
            mimeType: 'audio/pcm'
        };
        
        const audioProcessor = new MockAudioProcessor();
        
        const result = await forwardAudio('test-session', audio, connectionData, audioProcessor);
        
        assert.strictEqual(result, true);
        assert.strictEqual(connectionData.sequenceNumber, 1); // Wrapped to 0 when sent, then incremented to 1
    });

    await t.test('Cleanup audio forwarding', () => {
        const connectionData = {
            audioForwardingEnabled: true,
            sequenceNumber: 100,
            lastAudioSent: Date.now()
        };
        
        cleanupAudioForwarding('test-session', connectionData);
        
        assert.strictEqual(connectionData.audioForwardingEnabled, false);
        assert.strictEqual(connectionData.sequenceNumber, 0);
        assert.strictEqual(connectionData.lastAudioSent, 0);
    });
});
