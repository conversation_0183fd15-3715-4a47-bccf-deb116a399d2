import { describe, test } from 'node:test';
import assert from 'node:assert';
import { Logger, LogLevel, Component } from '../src/utils/logger.ts';

// Basic tests for the Logger utility

describe('Logger Utility', () => {
  test('formatMessage returns valid JSON when enabled', () => {
    const log = new Logger({ json: true, level: LogLevel.DEBUG });
    const msg = log.formatMessage(LogLevel.INFO, 'test', { foo: 'bar' }, 'CA123', Component.API);
    const parsed = JSON.parse(msg);

    assert.strictEqual(parsed.message, 'test');
    assert.strictEqual(parsed.foo, 'bar');
    assert.strictEqual(parsed.callSid, 'CA123');
    assert.strictEqual(parsed.component, Component.API);
    assert.ok(parsed.timestamp);
  });

  test('child logger inherits settings and sets component', () => {
    const parent = new Logger({ level: LogLevel.WARN });
    const child = parent.child(Component.TWILIO);

    assert.strictEqual(child.level, LogLevel.WARN);
    assert.strictEqual(child.component, Component.TWILIO);
    assert.strictEqual(child.enableJson, parent.enableJson);
  });

  test('setLevel updates the log level', () => {
    const log = new Logger({ level: LogLevel.INFO });
    log.setLevel(LogLevel.DEBUG);
    assert.strictEqual(log.level, LogLevel.DEBUG);
  });

  test('performance timer measures duration', async () => {
    const log = new Logger({ level: LogLevel.DEBUG });
    const timer = log.startTimer('operation');
    await new Promise(resolve => setTimeout(resolve, 5));
    const duration = timer.end();
    assert.ok(duration >= 0);
  });
});
