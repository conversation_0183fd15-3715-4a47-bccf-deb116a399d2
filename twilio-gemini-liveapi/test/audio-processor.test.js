import { describe, it, before, after, beforeEach } from 'node:test';
import assert from 'node:assert';
import { AudioProcessor } from '../src/audio/audio-processor.ts';

describe('AudioProcessor', () => {
    let audioProcessor;

    beforeEach(() => {
        audioProcessor = new AudioProcessor();
    });

    describe('μ-law to PCM conversion', () => {
        it('should convert μ-law to PCM16 correctly', () => {
            // Test with known μ-law values
            const ulawData = Buffer.from([0x80, 0x81, 0x82, 0x83]); // Sample μ-law values
            const pcmData = audioProcessor.convertUlawToPCM(ulawData);

            assert.ok(Buffer.isBuffer(pcmData));
            assert.strictEqual(pcmData.length, ulawData.length * 2); // PCM16 is 2 bytes per sample
        });

        it('should handle empty input', () => {
            const ulawData = Buffer.from([]);
            const pcmData = audioProcessor.convertUlawToPCM(ulawData);

            assert.ok(Buffer.isBuffer(pcmData));
            assert.strictEqual(pcmData.length, 0);
        });

        it('should handle large buffers without memory issues', () => {
            const largeUlawData = Buffer.alloc(44100); // 1 second of audio at 44.1kHz
            largeUlawData.fill(0x80); // Fill with silence

            const pcmData = audioProcessor.convertUlawToPCM(largeUlawData);

            assert.ok(Buffer.isBuffer(pcmData));
            assert.strictEqual(pcmData.length, largeUlawData.length * 2);
        });

        it('should produce consistent output for same input', () => {
            const ulawData = Buffer.from([0x80, 0x81, 0x82, 0x83]);
            const pcmData1 = audioProcessor.convertUlawToPCM(ulawData);
            const pcmData2 = audioProcessor.convertUlawToPCM(ulawData);

            assert.deepStrictEqual(pcmData1, pcmData2);
        });
    });

    describe('PCM to μ-law conversion', () => {
        it('should convert PCM16 to μ-law correctly', () => {
            // Test with known PCM16 values (little-endian)
            const pcmData = Buffer.from([0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04]);
            const ulawData = audioProcessor.pcmToUlaw(pcmData);

            assert.ok(Buffer.isBuffer(ulawData));
            assert.strictEqual(ulawData.length, pcmData.length / 2); // μ-law is 1 byte per sample
        });

        it('should handle empty input', () => {
            const pcmData = Buffer.from([]);
            const ulawData = audioProcessor.pcmToUlaw(pcmData);

            assert.ok(Buffer.isBuffer(ulawData));
            assert.strictEqual(ulawData.length, 0);
        });

        it('should handle uneven length input gracefully', () => {
            // PCM16 should have even length (2 bytes per sample)
            const pcmData = Buffer.from([0x00, 0x01, 0x00]); // 3 bytes (uneven)
            const ulawData = audioProcessor.pcmToUlaw(pcmData);

            assert.ok(Buffer.isBuffer(ulawData));
            assert.strictEqual(ulawData.length, 1); // Should process only the complete sample
        });
    });

    describe('round-trip conversion', () => {
        it('should maintain reasonable quality after round-trip conversion', () => {
            // Start with some PCM16 data
            const originalPcm = Buffer.from([
                0x00, 0x10, 0x00, 0x20, 0x00, 0x30, 0x00, 0x40,
                0x00, 0x50, 0x00, 0x60, 0x00, 0x70, 0x00, 0x80
            ]);

            // Convert to μ-law and back
            const ulawData = audioProcessor.pcmToUlaw(originalPcm);
            const convertedPcm = audioProcessor.convertUlawToPCM(ulawData, true); // skip enhancement for test

            assert.strictEqual(convertedPcm.length, originalPcm.length);
            
            // Check that values are reasonably close (μ-law is lossy)
            for (let i = 0; i < originalPcm.length; i += 2) {
                const original = originalPcm.readInt16LE(i);
                const converted = convertedPcm.readInt16LE(i);
                
                // Allow some tolerance due to μ-law compression
                const diff = Math.abs(original - converted);
                // μ-law is very lossy for small values, allow up to 50% difference
                const percentDiff = Math.abs(original) > 0 ? (diff / Math.abs(original)) * 100 : 0;
                assert.ok(diff < 5000 || percentDiff < 50, `Difference too large: ${diff} (${percentDiff.toFixed(1)}%) at position ${i}`);
            }
        });
    });

    describe('WebM to PCM conversion', () => {
        it('should handle WebM conversion attempt', () => {
            // Mock WebM data (would normally be actual WebM bytes)
            const webmData = Buffer.from([0x1A, 0x45, 0xDF, 0xA3]); // WebM header start
            
            // This should either convert or throw a meaningful error
            try {
                const pcmData = AudioProcessor.convertWebmToPCM16(webmData);
                assert.ok(Buffer.isBuffer(pcmData));
            } catch (error) {
                // Log the actual error message for debugging
                console.log('WebM conversion error:', error.message);
                assert.ok(error.message.includes('WebM') || error.message.includes('not implemented') || error.message.includes('Expected PCM'));
            }
        });
    });

    describe('error handling', () => {
        it('should handle null/undefined input gracefully', () => {
            const result1 = audioProcessor.convertUlawToPCM(null);
            assert.ok(Buffer.isBuffer(result1));
            assert.strictEqual(result1.length, 0);

            const result2 = audioProcessor.convertUlawToPCM(undefined);
            assert.ok(Buffer.isBuffer(result2));
            assert.strictEqual(result2.length, 0);
        });

        it('should handle invalid data types', () => {
            const result1 = audioProcessor.convertUlawToPCM("not a buffer");
            assert.ok(Buffer.isBuffer(result1));
            assert.strictEqual(result1.length, 0);

            const result2 = audioProcessor.convertUlawToPCM(123);
            assert.ok(Buffer.isBuffer(result2));
            assert.strictEqual(result2.length, 0);
        });

        it('should handle corrupted data gracefully', () => {
            // Create some potentially problematic data
            const corruptedData = Buffer.alloc(1000);
            for (let i = 0; i < corruptedData.length; i++) {
                corruptedData[i] = Math.floor(Math.random() * 256);
            }

            // Should not throw, should handle gracefully
            const result = audioProcessor.convertUlawToPCM(corruptedData);
            assert.ok(Buffer.isBuffer(result));
            assert.strictEqual(result.length, corruptedData.length * 2);
        });
    });

    describe('performance', () => {
        it('should process audio within reasonable time limits', () => {
            const largeBuffer = Buffer.alloc(44100); // 1 second of audio
            largeBuffer.fill(0x80);

            const startTime = Date.now();
            const result = audioProcessor.convertUlawToPCM(largeBuffer);
            const endTime = Date.now();

            const processingTime = endTime - startTime;
            
            // Should process 1 second of audio in less than 100ms
            assert.ok(processingTime < 100, `Processing took too long: ${processingTime}ms`);
            assert.ok(Buffer.isBuffer(result));
        });

        it('should handle multiple concurrent conversions', async () => {
            const testData = Buffer.alloc(1000);
            testData.fill(0x80);

            const promises = [];
            for (let i = 0; i < 10; i++) {
                promises.push(Promise.resolve(audioProcessor.convertUlawToPCM(testData)));
            }

            const results = await Promise.all(promises);
            
            // All should complete successfully
            assert.strictEqual(results.length, 10);
            results.forEach(result => {
                assert.ok(Buffer.isBuffer(result));
                assert.strictEqual(result.length, 2000);
            });
        });
    });

    describe('memory management', () => {
        it('should not leak memory with repeated conversions', () => {
            const testData = Buffer.alloc(1000);
            testData.fill(0x80);

            const initialMemory = process.memoryUsage().heapUsed;

            // Perform many conversions
            for (let i = 0; i < 1000; i++) {
                const result = audioProcessor.convertUlawToPCM(testData);
                // Don't keep reference to result to allow GC
            }

            const afterMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = afterMemory - initialMemory;

            // Memory increase should be reasonable (less than 10MB)
            assert.ok(memoryIncrease < 10 * 1024 * 1024, `Memory increase too high: ${memoryIncrease} bytes`);
        });
    });

    describe('edge cases', () => {
        it('should handle maximum and minimum μ-law values', () => {
            const extremeValues = Buffer.from([0x00, 0x7F, 0x80, 0xFF]);
            const result = audioProcessor.convertUlawToPCM(extremeValues);

            assert.ok(Buffer.isBuffer(result));
            assert.strictEqual(result.length, 8);
        });

        it('should handle single byte input', () => {
            const singleByte = Buffer.from([0x80]);
            const result = audioProcessor.convertUlawToPCM(singleByte);

            assert.ok(Buffer.isBuffer(result));
            assert.strictEqual(result.length, 2);
        });

        it('should handle very large buffers', () => {
            const largeBuffer = Buffer.alloc(1024 * 1024); // 1MB
            largeBuffer.fill(0x80);

            const result = audioProcessor.convertUlawToPCM(largeBuffer);

            assert.ok(Buffer.isBuffer(result));
            assert.strictEqual(result.length, largeBuffer.length * 2);
        });
    });
});