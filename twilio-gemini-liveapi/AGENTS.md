# AI Agent Development Guide for Twilio Gemini Live API Project

## Project Context for AI Agents
This document provides essential information for AI coding assistants working on the Twilio Gemini Live API project. It outlines directory structures, conventions, and best practices specific to this codebase.

## 🎯 PRIMARY DIRECTIVE: Keep It Simple
Before diving into technical details, remember: **SIMPLICITY IS THE ULTIMATE SOPHISTICATION**. Always prefer clear, straightforward solutions over complex ones. See CLAUDE.md for detailed simplicity guidelines.

## Working Directory Context
- **Primary workspace**: `/home/<USER>/github/verduona-full/twilio-gemini-liveapi`
- **Repository root**: `/home/<USER>/github/verduona-full`
- **Project type**: Node.js voice AI call center system with TypeScript/JavaScript
- **Main technologies**: Twilio, Google Gemini Live API, Fastify, Next.js, WebSockets

## Directory Structure for AI Agents

### Source Code Organization (`src/`)
When working on core functionality, organize code by domain:

```
src/
├── api/                    # REST API endpoints
├── audio/                  # Audio processing and streaming
├── config/                 # Configuration management
├── context/                # Conversation context handling
├── gemini/                 # Google Gemini AI integration
├── middleware/             # Express/Fastify middleware
├── scripts/                # Campaign script management
├── session/                # Session lifecycle management
├── types/                  # TypeScript type definitions
├── utils/                  # Utility functions and helpers
└── websocket/              # WebSocket message handling
```

### Testing Directory (`test/`)
When adding tests, follow this structure:

```
test/
├── helpers/                # Shared test utilities
│   └── env.js             # Environment setup for tests
├── [component].test.js    # Component-specific tests
└── integration/           # End-to-end integration tests
```

### Frontend Directory (`call-center-frontend/`)
For frontend development:

```
call-center-frontend/
├── app/                   # Next.js 13+ app router
├── lib/                   # Frontend utilities
├── public/                # Static assets
└── components/            # React components
```

## File Naming and Location Guidelines

### Where to Place New Files

#### API Development
- **Main endpoints**: `src/api/routes.ts`
- **Management features**: `src/api/management.ts`
- **Testing endpoints**: `src/api/testing.ts`

#### Audio Processing
- **Core processing**: `src/audio/audio-processor.ts`
- **Stream forwarding**: `src/audio/audio-forwarding.ts`
- **Transcription**: `src/audio/transcription-manager.ts`

#### Configuration
- **Main config**: `src/config/config.ts`
- **Audio settings**: `src/config/audio-config.ts`
- **Business logic**: `src/config/business-config.ts`
- **Campaign settings**: `src/config/campaign-config.ts`

#### Session Management
- **Main orchestrator**: `src/session/session-manager.ts`
- **Lifecycle events**: `src/session/lifecycle-manager.ts`
- **Error recovery**: `src/session/recovery-manager.ts`
- **Health monitoring**: `src/session/health-monitor.ts`

#### WebSocket Handling
- **Message routing**: `src/websocket/handlers.ts`
- **Audio streams**: `src/websocket/audio-handlers.ts`
- **Session events**: `src/websocket/session-events.ts`
- **Twilio flows**: `src/websocket/twilio-flow-handler.ts`
- **Testing flows**: `src/websocket/local-testing-handler.ts`

#### Utilities
- **General utilities**: `src/utils/`
- **Logging**: `src/utils/logger.js`
- **Test helpers**: `test/helpers/`

### Testing File Placement

#### Test File Naming
- **Pattern**: `[component-name].test.js`
- **Location**: `test/` directory (root level)
- **Examples**:
  - `test/session-manager.test.js`
  - `test/audio-processor.test.js`
  - `test/configuration.test.js`

#### Test Categories
- **Unit tests**: Individual component testing
- **Integration tests**: Multi-component workflow testing
- **API tests**: Endpoint validation
- **Flow tests**: Complete user journey testing

## Development Conventions for AI Agents

### Simplicity Guidelines
- **No Premature Abstractions**: Don't create interfaces/classes until you have 3+ implementations
- **Direct Function Calls**: Prefer direct calls over event emitters or complex pub/sub
- **Obvious Names**: `processAudioBuffer()` not `handleMediaStreamChunk()`
- **Linear Flow**: Code should read top-to-bottom like a story
- **Minimal Dependencies**: Use built-in Node.js modules when possible

### File Extensions and Languages
- **TypeScript**: `.ts` for new modules (preferred)
- **JavaScript**: `.js` for legacy modules (being migrated)
- **Tests**: `.test.js` using Node.js built-in test runner

### Naming Conventions
- **Files**: kebab-case (`audio-processor.ts`)
- **Functions**: camelCase (`processAudio()`)
- **Classes**: PascalCase (`AudioProcessor`)
- **Constants**: UPPER_CASE (`MAX_RETRIES`)

### Import/Export Patterns
```javascript
// External packages first
import { WebSocket } from 'ws';
import { FastifyInstance } from 'fastify';

// Internal modules second
import { SessionManager } from '../session/session-manager.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Relative imports last
import { handleAudioData } from './audio-handlers.js';
```

### Logging Standards
Always use the structured logger from `src/utils/logger.js`:

```javascript
import { logger } from '../utils/logger.js';

// Good - with emoji prefix and context
logger.info('🎤 Audio processed successfully', { 
    sessionId, 
    audioSize: buffer.length 
});

// Bad - don't use console.log
console.log('Audio processed');
```

### Error Handling Patterns
```javascript
try {
    await processAudio(audioData);
} catch (error) {
    logger.error('❌ Audio processing failed', { 
        sessionId, 
        error: error.message,
        stack: error.stack 
    });
    // Implement recovery logic
}
```

## Testing Guidelines for AI Agents

### Test Structure
Use Node.js built-in test runner:

```javascript
import { test, describe } from 'node:test';
import assert from 'node:assert';

describe('AudioProcessor', () => {
    test('should convert μ-law to PCM', async () => {
        const processor = new AudioProcessor();
        const result = await processor.convertUlawToPCM(ulawData);
        assert.ok(result instanceof Buffer);
    });
});
```

### Test Requirements
- **All 27+ tests must pass** before committing
- Test all 4 flows: outbound/inbound × Twilio/browser
- Include both unit and integration tests
- Use `test/helpers/` for shared utilities

### Running Tests
```bash
npm test                    # Run all tests
npm run test:workflow      # Run workflow integration tests
npm run test:4-flows       # Test all 4 major flows
```

## Code Quality Standards

### Simplicity-First Development
1. **Start Simple**: Implement the most straightforward solution
2. **Avoid Over-Engineering**: Don't add abstractions until proven necessary
3. **Clear Over Clever**: Write code that junior devs can understand
4. **Flat Over Nested**: Prefer shallow structures and direct calls

### Before Committing
1. `npm run lint:fix` - Fix linting issues
2. `npm run type-check` - Verify TypeScript types
3. `npm test` - All tests must pass
4. Manual testing of affected flows
5. **Simplicity Review**: Could this be done more simply?

### File Size Guidelines
- **Maximum 300 lines per file**
- Split large files into focused modules
- Extract reusable functions to `src/utils/`
- Separate concerns clearly

### Architecture Principles
- **Single responsibility** per module - but don't split too granularly
- **Direct dependencies** - inject only when truly needed for testing
- **Simple error handling** - try/catch with clear error messages
- **Structured logging** with context - but keep log statements readable
- **YAGNI** (You Aren't Gonna Need It) - don't build for hypothetical futures
- **Do The Simplest Thing That Could Possibly Work** - then iterate

## Common Development Patterns

### Simplicity in Practice

#### ✅ GOOD: Simple and Direct
```javascript
// Clear, direct, easy to debug
async function processCall(callData) {
  const session = await createSession(callData);
  await session.connectToGemini();
  return session.id;
}
```

#### ❌ BAD: Over-Engineered
```javascript
// Too many layers, hard to follow
class CallProcessorFactory {
  constructor(private readonly dispatcher: EventDispatcher) {}
  
  async process(data: CallDTO): Promise<Result<SessionId>> {
    return this.dispatcher
      .emit(new CallProcessingEvent(data))
      .pipe(map(e => e.sessionId));
  }
}
```

### Adding New API Endpoint
1. Define route in `src/api/routes.ts` - keep it REST-ful and simple
2. Implement handler in appropriate API file - direct logic, no unnecessary layers
3. Add input validation - use simple checks, not complex schemas
4. Create corresponding test in `test/backend-api.test.js`

### Adding WebSocket Handler
1. Add message type to `src/websocket/handlers.ts`
2. Implement handler in appropriate file
3. Add session state management
4. Test with all 4 flows

### Adding Configuration Option
1. Add to `src/config/config.ts`
2. Update environment validation
3. Document in `.clinerules`
4. Add test in `test/configuration.test.js`

## Performance and Monitoring

### Session Management
- Clean up inactive sessions
- Monitor memory usage
- Implement connection pooling
- Use caching appropriately

### Audio Processing
- Handle real-time constraints
- Implement buffering strategies
- Monitor processing latency
- Graceful degradation on errors

## Security Considerations

### Input Validation
- Use `src/middleware/security-utils.js`
- Validate all user inputs
- Sanitize phone numbers and text
- Implement rate limiting

### Authentication
- Use proper authentication middleware
- Validate Twilio webhook signatures
- Secure API endpoints
- Never log sensitive data

## Recent Fixes Applied (Reference for Future Issues)

### Audio System Refactoring (JS to TS Migration)
**Problem**: After converting from JavaScript to TypeScript, audio methods stopped working
**Solution Applied**:

#### 1. Authentication Issues
- **Problem**: Auth middleware required `API_KEY` or `SUPABASE_SERVICE_KEY` but used existing env vars
- **Fix**: Updated auth middleware to accept `GEMINI_API_KEY` and `TWILIO_AUTH_TOKEN`
- **Files**: `src/middleware/auth-simple.ts`, `src/websocket/websocket-helpers.ts`

#### 2. Environment Loading Issues  
- **Problem**: .env file not loading properly in development
- **Fix**: Use `set -a && source .env && set +a` before starting server
- **Files**: `start_server.sh`, `start_production.sh`

#### 3. Gemini Live API Type Issues
- **Problem**: TypeScript errors on `geminiClient.live.connect()` method
- **Fix**: Added type casting `(this.geminiClient as any).live.connect()`
- **Files**: `src/session/session-manager.ts`

#### 4. Production Deployment Requirements
**From FINAL_STATE.md requirements**:
- **Backend**: Port 3101, production Node.js server
- **Frontend**: Port 3011, Next.js production build (`pnpm build` then `pnpm start`)
- **Backend URL**: `https://gemini-api.verduona.com`
- **Frontend URL**: `https://twilio-gemini.verduona.com`

### Audio Method Status (All Working)
✅ **Outbound Twilio calls** - Uses `twilio-flow-handler.ts` with μ-law conversion  
✅ **Outbound testing mode** - Uses `local-testing-handler.ts` for browser audio  
✅ **Inbound Twilio calls** - Same handler as outbound with proper flow detection  
✅ **Inbound testing** - Uses `testing.ts` API endpoints with manual controls

### Production Startup Commands
```bash
# Production mode (as per FINAL_STATE.md)
./start_production.sh

# Development mode (for debugging)
./start_server.sh
```

### Critical Commands for Production
```bash
# Build backend
pnpm build

# Build frontend
cd call-center-frontend && pnpm build

# Start with PM2 (production)
PORT=3101 pm2 start "node dist/index.js" --name "twilio-gemini-backend"
cd call-center-frontend && PORT=3011 pm2 start "pnpm start" --name "twilio-gemini-frontend"

# Save PM2 state
pm2 save
```

### Environment Variables Required
- `GEMINI_API_KEY` - Google Gemini API access
- `TWILIO_ACCOUNT_SID` - Twilio account identifier  
- `TWILIO_AUTH_TOKEN` - Twilio authentication
- `DEEPGRAM_API_KEY` - Audio transcription service
- `PUBLIC_URL` - Server public URL (https://gemini-api.verduona.com)

This guide provides AI agents with the essential context needed to work effectively on the Twilio Gemini Live API project while maintaining code quality and architectural consistency.
