# 🤖 Roo Code AI Assistant Guide for Twilio Gemini Live API

This guide is specifically designed for Roo Code AI assistants to quickly understand and work with this codebase.

## 🎯 Project Overview

**What This Project Does**: Voice AI call center system that connects phone calls to Google's Gemini AI
- Handles both inbound and outbound calls
- Real-time audio streaming via WebSockets
- 4 call flows: T<PERSON><PERSON> (inbound/outbound) and Browser testing (inbound/outbound)

## 🚦 Quick Start for AI Assistants

### 1. Check System Status
```bash
# See what's running
pm2 status

# Check system health
./health-check.sh

# View recent logs
pm2 logs --lines 50
```

### 2. Understand the Architecture
- **Backend**: Node.js/TypeScript on port 3101
- **Frontend**: Next.js on port 3011
- **Process Manager**: PM2 with ecosystem.config.cjs
- **Main Entry**: index.js (uses tsx for TypeScript)

### 3. Key Files to Know
```
index.js                          # Main server entry
src/session/session-manager.ts    # Core session handling
src/gemini/client.ts             # AI integration
src/websocket/handlers.ts        # WebSocket routing
src/audio/audio-processor.ts     # Audio conversion
ecosystem.config.cjs             # PM2 configuration
```

## 🔍 Debugging Cheat Sheet

### Finding Logs by Emoji
```bash
# Startup issues
pm2 logs | grep "🚀"

# Call problems
pm2 logs | grep "📞"

# Audio issues
pm2 logs | grep "🎤"

# AI problems
pm2 logs | grep "💬"

# Errors only
pm2 logs | grep "❌"

# Cleanup/Memory
pm2 logs | grep "🧹"
```

### Common Issues & Solutions

#### Backend Won't Start
```bash
# Check logs
pm2 logs twilio-gemini-backend --lines 100

# Common fix: TypeScript issues
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
pm2 delete twilio-gemini-backend
pm2 start ecosystem.config.cjs --only twilio-gemini-backend
```

#### Frontend Build Errors
```bash
cd call-center-frontend
pnpm install --config.strict-peer-dependencies=false
pnpm build
```

#### Memory/Restart Issues
```bash
# Check memory usage
pm2 monit

# If high memory, restart with limits
pm2 restart twilio-gemini-backend --max-memory-restart 1G
```

## 🚀 Deployment Process

### Always Use the Script
```bash
# This handles EVERYTHING correctly
./deploy-production.sh
```

### What the Script Does
1. ✅ Sets up PM2 log rotation (prevents disk full)
2. ✅ Configures memory limits (prevents crashes)
3. ✅ Builds frontend for production
4. ✅ Restarts services properly
5. ✅ Runs health checks
6. ✅ Saves PM2 state

## 📋 Testing Checklist

Before any deployment:
```bash
# Run all tests (27+ must pass)
npm test

# Check linting
npm run lint

# Test all 4 call flows manually
# 1. Outbound Twilio call
# 2. Inbound Twilio call  
# 3. Browser test outbound
# 4. Browser test inbound
```

## 🎯 Campaign Scripts

**CRITICAL**: This system uses campaign scripts ONLY (no system prompts)
- Scripts 1-6: Outbound campaigns
- Scripts 7-12: Inbound campaigns
- Located in: Database (managed via API)
- Endpoint: `/api/campaign-scripts/{id}`

## 🛠️ Environment Variables

Required in .env:
```
GEMINI_API_KEY=xxx
TWILIO_ACCOUNT_SID=xxx
TWILIO_AUTH_TOKEN=xxx
PUBLIC_URL=https://gemini-api.verduona.com
PORT=3101
```

## 📊 Monitoring Production

```bash
# Real-time monitoring
pm2 monit

# Check specific service
pm2 describe twilio-gemini-backend

# View metrics
pm2 info twilio-gemini-backend

# Check disk space
df -h

# Check memory
free -h
```

## 🚨 Emergency Commands

```bash
# If everything is broken
pm2 kill                          # Stop all PM2 processes
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
./deploy-production.sh            # Redeploy everything

# If logs are too big
pm2 flush                         # Clear all logs
pm2 restart all                   # Restart services

# If out of memory
pm2 restart all --max-memory-restart 1G
```

## 💡 Pro Tips for AI Assistants

1. **Always check logs first** - They have emoji prefixes for easy scanning
2. **Use the deployment script** - Don't try to deploy manually
3. **Test after changes** - All 27+ tests must pass
4. **Monitor memory** - This prevented 337+ crashes in production
5. **Follow simplicity rules** - See CLAUDE.md for guidelines
6. **Never use console.log** - Use the structured logger with emojis

## 📚 Additional Documentation

- **.roorules**: Complete project rules and instructions
- **CLAUDE.md**: Simplicity guidelines
- **DEPLOYMENT_README.md**: Detailed deployment steps
- **CAMPAIGN_SCRIPT_POLICY.md**: AI instruction policy

## 🔗 Quick Links

- Backend Health: https://gemini-api.verduona.com/health
- Frontend: https://twilio-gemini.verduona.com
- PM2 Logs Location: ~/.pm2/logs/
- Project Root: /home/<USER>/github/verduona-full/twilio-gemini-liveapi

---

**Remember**: When in doubt, check the logs with emoji prefixes! 🔍