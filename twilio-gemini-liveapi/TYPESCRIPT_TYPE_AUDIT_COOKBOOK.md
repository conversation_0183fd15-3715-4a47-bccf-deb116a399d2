# TypeScript Type Alignment Audit Cookbook

## Instructions for AI Coding Assistant

### Initial Discovery Phase

**1. Search for Duplicate Type/Interface Definitions**
```
Find all interfaces and types that might be defined multiple times across the codebase:
- Search for "interface [name]" patterns
- Look for interfaces with the same name in different files
- Check for "type [name] =" definitions that might conflict
- Pay special attention to commonly duplicated names like Config, Options, State, Data
```

**2. Identify Type Inconsistencies**
```
Look for these specific patterns of type misalignment:
- Interfaces extending base types but adding conflicting properties
- Zod schemas vs TypeScript interfaces for the same data
- String literals vs enums vs union types for the same concept
- Optional vs required fields for the same property across files
- Nullable handling inconsistencies (T | null vs T | undefined vs T?)
```

**3. Find 'any' Type Usage**
```
Search for problematic type patterns:
- ": any" - parameter or return types
- "as any" - type assertions
- "<any>" - generic type arguments
- "Promise<any>" - untyped promises
- catch blocks without typed errors
- Function parameters without types
```

### Deep Analysis Phase

**4. Check Import/Export Consistency**
```
Verify type imports across files:
- Types imported from different locations but representing same data
- Missing type exports that force duplications
- Circular type dependencies
- Types defined locally that should be shared
```

**5. Analyze Enum and Literal Type Usage**
```
Look for inconsistent constant definitions:
- String literals used directly vs defined constants
- Same values defined differently ('incoming' vs 'inbound')
- Enums vs union types for same concept
- Magic strings that should be typed constants
```

**6. Examine Complex Type Relationships**
```
Check for structural issues:
- Base types with multiple extended versions
- Discriminated unions with inconsistent discriminators
- Generic types with inconsistent constraints
- Mapped types that don't align with source types
```

### Validation Phase

**7. Cross-Reference Runtime vs Compile-Time Types**
```
Ensure runtime validation matches TypeScript types:
- Zod schemas vs TypeScript interfaces
- JSON schema definitions vs types
- API response types vs frontend expectations
- Database models vs application types
```

**8. Check Type Assertions and Guards**
```
Look for unsafe type operations:
- Type assertions without validation
- Missing type guards for union types
- Incorrect discriminator checks
- as unknown as Type patterns
```

### Specific Patterns to Find

**9. Common Problematic Patterns**
```typescript
// Look for these specific issues:

// 1. Duplicate interface definitions
interface Config { ... }  // in file A
interface Config { ... }  // in file B with different properties

// 2. Inconsistent optional properties
interface User {
  name: string;      // required in one file
  name?: string;     // optional in another
}

// 3. String literal inconsistencies
type Status = 'active' | 'inactive';  // in one file
status === 'enabled'                   // used elsewhere

// 4. Conflicting type sources
import { SessionConfig } from '../types';     // in one file
import { SessionConfig } from './local-types'; // in another

// 5. Mismatched nullable types
field: string | null        // in interface
field: z.string().optional() // in Zod schema
```

### Fix Priority Order

**10. Systematic Fix Approach**
```
1. Fix literal type mismatches first (easiest, high impact)
2. Consolidate duplicate interfaces into single source of truth
3. Replace 'any' with proper types
4. Align optional/required properties
5. Fix nullable/undefined inconsistencies
6. Update type assertions to match new types
7. Run TypeScript compiler after each major change
```

### Verification Commands

**11. Use These Commands Throughout**
```bash
# Find duplicate interface names
grep -r "interface " src/ --include="*.ts" | awk '{print $2}' | sort | uniq -c | sort -nr

# Find 'any' usage
grep -r ": any" src/ --include="*.ts" | grep -v "\.d\.ts"

# Find type assertions
grep -r " as " src/ --include="*.ts" | grep -v "\.d\.ts"

# Run TypeScript check
npx tsc --noEmit

# Check specific file
npx tsc --noEmit path/to/file.ts
```

### Final Checklist

**12. Before Declaring Complete**
- [ ] All duplicate type definitions consolidated
- [ ] No unnecessary 'any' types remain
- [ ] All string literals replaced with proper types
- [ ] Zod schemas align with TypeScript types
- [ ] Optional/required properties are consistent
- [ ] TypeScript compiles without errors
- [ ] No type assertions hiding real issues
- [ ] Imports all reference canonical type locations

### Example Instruction to AI

> "Please perform a comprehensive TypeScript type alignment audit on this codebase. Start by searching for duplicate interface definitions, then identify any type inconsistencies like mismatched optional properties, string literals that should be enums, and unnecessary 'any' types. Pay special attention to types that cross file boundaries - ensure imports are consistent and types match their runtime validators (like Zod schemas). Fix issues in priority order: literals first, then consolidate duplicates, then replace 'any' types. Run tsc --noEmit after each major fix to ensure you're not breaking anything."

### Additional Tips for AI Assistant

1. **Use TodoWrite tool** - Track your progress through the audit with a todo list
2. **Batch similar fixes** - Group related type fixes together
3. **Test incrementally** - Run TypeScript check after each major change
4. **Document decisions** - Comment on why certain type decisions were made
5. **Consider runtime impact** - Some type changes may require runtime validation updates

### Common Gotchas

- **Type widening** - Be careful not to make types too permissive when fixing
- **Breaking changes** - Some type fixes may break existing code
- **External dependencies** - Can't change types from node_modules
- **Generated files** - Don't modify auto-generated type definitions
- **Test files** - May have intentionally loose types for testing

### Success Metrics

A successful type audit results in:
- Zero TypeScript compilation errors
- Consistent type usage across all files
- No duplicate type definitions
- Minimal use of 'any' type
- Clear separation between nullable and optional
- Runtime validators matching compile-time types