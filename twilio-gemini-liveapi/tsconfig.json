{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "allowJs": true,
    "outDir": "./dist",
    "rootDir": "./",
    "strict": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": false,
    "incremental": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/config/*": ["./src/config/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/gemini/*": ["./src/gemini/*"],
      "@/session/*": ["./src/session/*"],
      "@/audio/*": ["./src/audio/*"],
      "@/websocket/*": ["./src/websocket/*"],
      "@/api/*": ["./src/api/*"],
      "@/middleware/*": ["./src/middleware/*"],
      "@/scripts/*": ["./src/scripts/*"],
      "@/types/*": ["./src/types/*"]
    },
    "types": ["node"],
    "lib": ["ES2022", "DOM"],
    // Additional strict type checking options
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false
  },
  "include": [
    "src/**/*",
    "index.ts",
    "*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "test/**/*",
    "**/*.test.ts",
    "**/*.test.js",
    "call-center-frontend/**/*",
    "audio-debug/**/*",
    "data/**/*",
    "middleware/*.js"
  ],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  }
}
