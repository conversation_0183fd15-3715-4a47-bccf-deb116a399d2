const fs = require('fs');
const path = require('path');

const testDir = './test';
const files = fs.readdirSync(testDir).filter(f => f.endsWith('.test.js'));

const testsNeedingEnv = [
    'audio-forwarding-integration.test.js',
    'authentication.test.js',
    'critical-fixes.test.js',
    'memory-management-validation.test.js',
    'message-router.test.js',
    'script-cache.test.js',
    'session-manager.test.js',
    'summary-generator.test.js'
];

testsNeedingEnv.forEach(file => {
    const filePath = path.join(testDir, file);
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // Check if env helper is already imported
    if (!content.includes("import './helpers/env.js'")) {
        // Add it as the first line
        content = "import './helpers/env.js';\n" + content;
        fs.writeFileSync(filePath, content);
        console.log('Added env helper to ' + file);
    } else {
        console.log('Env helper already present in ' + file);
    }
});

console.log('Done adding env helpers');