/**
 * Shared Auth Integration for Twilio Gemini API
 * Wrapper around the shared-auth utilities with TypeScript support
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { readFileSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load the shared-auth utilities dynamically
const sharedAuthPath = join(__dirname, '../../../shared-auth/auth-utils.js');
let sharedAuth: any;

try {
  // For ES modules, we need to use dynamic import
  const authUtilsCode = readFileSync(sharedAuthPath, 'utf-8');
  
  // Create a module wrapper to handle the shared auth utilities
  const moduleWrapper = `
    ${authUtilsCode}
    
    // Export all functions for our use
    export {
      getUserFromHeaders,
      isAuthenticated,
      getUserInfo,
      createFastifyAuthMiddleware,
      logAuthStatus,
      createUserContext,
      hasPermission,
      createSecureResponse
    };
  `;
  
  // Note: Dynamic module creation commented out due to security concerns
  // Consider using proper module loading instead of eval-based approaches
  
} catch (error) {
  console.warn('⚠️ Could not load shared-auth utilities, falling back to inline implementation');
}

// Interface definitions
interface User {
  id: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

interface AuthRequest extends FastifyRequest {
  user?: User | null;
}

/**
 * Extract user information from request headers (set by nginx auth_request)
 */
export function getUserFromHeaders(request: FastifyRequest): User | null {
  const userId = request.headers['x-user-id'] as string;
  const userEmail = request.headers['x-user-email'] as string;
  const userRole = request.headers['x-user-role'] as string;

  if (userId && userEmail) {
    return {
      id: userId,
      email: userEmail,
      role: userRole || 'authenticated',
      isAuthenticated: true
    };
  }

  return null;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(request: FastifyRequest): boolean {
  const user = getUserFromHeaders(request);
  return user !== null;
}

/**
 * Get user info with fallback for anonymous access
 */
export function getUserInfo(request: FastifyRequest): User {
  const user = getUserFromHeaders(request);
  
  if (user) {
    return user;
  }

  // Return anonymous user object
  return {
    id: 'anonymous',
    email: '',
    role: 'anonymous',
    isAuthenticated: false
  };
}

/**
 * Log authentication status for debugging
 */
export function logAuthStatus(request: FastifyRequest, serviceName: string = 'Twilio-Gemini'): void {
  const user = getUserFromHeaders(request);
  
  if (user) {
    console.log(`✅ [${serviceName}] User authenticated: ${user.email} (${user.id})`);
  } else {
    console.log(`ℹ️  [${serviceName}] Anonymous access - no user headers found`);
  }
}

/**
 * Create user context for frontend applications
 */
export function createUserContext(request: FastifyRequest) {
  const user = getUserInfo(request);
  
  return {
    isAuthenticated: user.isAuthenticated,
    user: user.isAuthenticated ? {
      id: user.id,
      email: user.email,
      role: user.role
    } : null
  };
}

/**
 * Middleware for Fastify with shared auth integration
 */
export async function validateSharedAuth(request: AuthRequest, reply: FastifyReply): Promise<void> {
  // Skip auth for WebSocket connections
  if (request.headers && request.headers.upgrade === 'websocket') {
    return;
  }

  // Skip auth for CORS preflight OPTIONS requests
  if (request.method === 'OPTIONS') {
    return;
  }

  // Public paths that don't need authentication (same as nginx-auth)
  const publicPaths = [
    '/incoming-call',        // Twilio webhook (verified by Twilio signature)
    '/call-status',          // Twilio webhook (verified by Twilio signature)
    '/recording-status',     // Twilio webhook (verified by Twilio signature)
    '/health',               // Health check endpoint
    '/ready',                // Kubernetes readiness check
    '/live',                 // Kubernetes liveness check
    '/available-voices',     // Voice configuration
    '/available-models',     // Model configuration
    '/audio-settings',       // Audio settings
    '/get-campaign-script',  // Campaign script loading (CRITICAL: needed for script loading)
    '/incoming-campaign',    // Incoming campaign scripts (CRITICAL: needed for script loading)
    '/api/validate-token',   // Token validation endpoint
    '/update-session-config', // Session configuration updates (needed for testing and development)
  ];

  // WebSocket paths that need special handling
  const websocketPaths = [
    '/media-stream'        // Twilio WebSocket (verified by Twilio)
  ];

  if (publicPaths.some(path => request.url.startsWith(path))) {
    return; // Continue without validation for public endpoints
  }

  if (websocketPaths.some(path => request.url.startsWith(path))) {
    return; // WebSocket auth handled separately
  }

  try {
    // Extract user information from nginx-set headers using shared auth
    const user = getUserFromHeaders(request);
    
    // Log authentication status
    logAuthStatus(request, 'Twilio-Gemini');
    
    // Check if nginx provided authentication headers
    if (!user) {
      // Check if we're in development mode and allow bypass
      const isDevelopment = process.env.NODE_ENV !== 'production';
      const allowUnauthenticated = process.env.ALLOW_UNAUTHENTICATED === 'true';
      
      if (isDevelopment && allowUnauthenticated) {
        console.warn('⚠️ No shared auth headers - allowing for development mode', {
          url: request.url,
          method: request.method
        });
        return;
      }

      console.error('❌ Missing shared auth headers for protected endpoint', {
        url: request.url,
        method: request.method,
        ip: request.ip
      });

      await reply.code(401).send({ 
        error: 'Authentication required',
        message: 'Access denied by authentication service'
      });
      return;
    }

    // Attach user info to request for downstream use
    request.user = user;

    console.log(`✅ Shared auth validation passed for ${user.email}`);

  } catch (error) {
    console.error('❌ Shared auth validation failed:', error);
    await reply.code(401).send({ 
      error: 'Authentication failed',  
      message: 'Invalid authentication headers'
    });
    return;
  }
}

/**
 * Create a secure response with user context
 */
export function createSecureResponse(data: any, request: FastifyRequest) {
  const userContext = createUserContext(request);
  
  return {
    ...data,
    user: userContext
  };
}

/**
 * Get authenticated user from request (helper function)
 */
export function getAuthenticatedUser(request: AuthRequest): User | null {
  return request.user || null;
}