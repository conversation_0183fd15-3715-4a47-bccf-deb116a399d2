/**
 * Global Error Handler Middleware
 * Ensures all errors are properly logged and propagated
 */

import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '../utils/logger';

export interface StandardErrorResponse {
    success: false;
    error: {
        message: string;
        code?: string;
        statusCode: number;
        timestamp: string;
        requestId?: string;
        details?: any;
    };
}

/**
 * Creates a standard error response
 */
export function createErrorResponse(
    error: Error | unknown,
    statusCode: number = 500,
    requestId?: string
): StandardErrorResponse {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorCode = (error as any)?.code || 'INTERNAL_ERROR';
    
    return {
        success: false,
        error: {
            message: errorMessage,
            code: errorCode,
            statusCode,
            timestamp: new Date().toISOString(),
            requestId,
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }
    };
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler(
    fn: (request: FastifyRequest, reply: FastifyReply) => Promise<any>
) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const result = await fn(request, reply);
            // If the handler didn't send a response, send the result
            if (!reply.sent && result !== undefined) {
                reply.send(result);
            }
        } catch (error) {
            // Log the error with context
            const errorData = {
                error: error instanceof Error ? error : new Error(String(error)),
                method: request.method,
                url: request.url,
                params: request.params,
                query: request.query,
                headers: {
                    'user-agent': request.headers['user-agent'],
                    'content-type': request.headers['content-type']
                }
            };
            logger.error('❌ Route handler error', errorData);
            
            // Send standardized error response
            const statusCode = (error as any)?.statusCode || 500;
            const errorResponse = createErrorResponse(
                error,
                statusCode,
                (request as any).id
            );
            
            reply.status(statusCode).send(errorResponse);
        }
    };
}

/**
 * WebSocket error handler
 */
export function handleWebSocketError(
    error: Error | unknown,
    sessionId: string,
    operation: string
): void {
    logger.error(`❌ WebSocket error during ${operation}`, {
        error: error instanceof Error ? error : new Error(String(error))
    }, sessionId);
    
    // Note: Custom error event emission can be added here if needed
    // For now, we rely on the structured logging
}

/**
 * Promise rejection handler
 */
export function handlePromiseRejection(
    promise: Promise<any>,
    context: string
): Promise<any> {
    return promise.catch((error) => {
        logger.error(`❌ Unhandled promise rejection in ${context}`, {
            error: error instanceof Error ? error : new Error(String(error))
        });
        throw error; // Re-throw to maintain error flow
    });
}

/**
 * Register global error handlers
 */
export function registerErrorHandlers(fastify: FastifyInstance): void {
    // Fastify error handler
    fastify.setErrorHandler((error, request, reply) => {
        logger.error('❌ Fastify error', {
            error,
            method: request.method,
            url: request.url,
            statusCode: error.statusCode || 500
        });
        
        const errorResponse = createErrorResponse(
            error,
            error.statusCode || 500,
            (request as any).id
        );
        
        reply.status(error.statusCode || 500).send(errorResponse);
    });
    
    // Not found handler
    fastify.setNotFoundHandler((request, reply) => {
        logger.warn('⚠️ Route not found', {
            method: request.method,
            url: request.url
        });
        
        reply.status(404).send({
            success: false,
            error: {
                message: 'Route not found',
                code: 'NOT_FOUND',
                statusCode: 404,
                timestamp: new Date().toISOString(),
                requestId: (request as any).id
            }
        });
    });
    
    // Global unhandled rejection handler
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('❌ Unhandled Promise Rejection', {
            error: reason instanceof Error ? reason : new Error(String(reason)),
            promise: promise.toString()
        });
    });
    
    // Global uncaught exception handler
    process.on('uncaughtException', (error) => {
        logger.error('❌ Uncaught Exception', {
            error,
            fatal: true
        });
        
        // Give time for logs to flush before exiting
        setTimeout(() => {
            process.exit(1);
        }, 1000);
    });
    
    logger.info('✅ Error handlers registered');
}

/**
 * Error logging hook
 */
export function registerErrorLoggingHook(fastify: FastifyInstance): void {
    fastify.addHook('onSend', async (request, reply, payload) => {
        const statusCode = reply.statusCode;
        if (statusCode >= 400) {
            logger.warn(`⚠️ Error response sent`, {
                method: request.method,
                url: request.url,
                statusCode,
                error: payload
            });
        }
        return payload;
    });
}

/**
 * Validation error formatter
 */
export function formatValidationError(error: any): StandardErrorResponse {
    if (error.validation) {
        return {
            success: false,
            error: {
                message: 'Validation failed',
                code: 'VALIDATION_ERROR',
                statusCode: 400,
                timestamp: new Date().toISOString(),
                details: error.validation
            }
        };
    }
    
    return createErrorResponse(error, 400);
}