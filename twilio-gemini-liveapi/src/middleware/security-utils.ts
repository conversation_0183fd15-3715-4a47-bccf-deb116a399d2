// Input validation and sanitization utilities

type RateLimitRequest = { timestamp: number };

export class SecurityUtils {
  private static rateLimitStore: Map<string, RateLimitRequest[]>;

  /**
   * Validate and sanitize phone number input
   * @param phoneNumber - Phone number to validate
   * @returns Sanitized phone number or null if invalid
   */
  static validatePhoneNumber(phoneNumber: unknown): string | null {
    if (!phoneNumber || typeof phoneNumber !== 'string') {return null;}

    // Remove all non-digit characters except + at the beginning
    const sanitized = phoneNumber.replace(/[^\d+]/g, '');

    // Basic validation: should start with + and have 10-15 digits
    if (!/^\+\d{10,15}$/.test(sanitized)) {return null;}

    return sanitized;
  }

  /**
   * Validate and sanitize text input
   * @param text - Text to validate
   * @param maxLength - Maximum allowed length
   * @returns Sanitized text or null if invalid
   */
  static validateText(text: unknown, maxLength: number = 1000): string | null {
    if (!text || typeof text !== 'string') {return null;}

    // Remove potentially dangerous characters
    const sanitized = text
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/data:/gi, '') // Remove data: protocol
      .trim();

    if (sanitized.length > maxLength) {return null;}

    return sanitized;
  }

  /**
   * Validate JSON input
   * @param jsonString - JSON string to validate
   * @param maxSize - Maximum size in bytes
   * @returns Parsed JSON or null if invalid
   */
  static validateJSON(jsonString: unknown, maxSize: number = 10240): Record<string, any> | null {
    if (!jsonString || typeof jsonString !== 'string') {return null;}

    if (jsonString.length > maxSize) {return null;}

    try {
      const parsed = JSON.parse(jsonString);

      // Additional validation for nested objects
      if (typeof parsed !== 'object' || parsed === null) {return null;}

      return parsed;
    } catch (error) {
      return null;
    }
  }

  /**
   * Rate limiting check for specific operations
   * @param key - Unique key for rate limiting
   * @param maxRequests - Maximum requests allowed
   * @param windowMs - Time window in milliseconds
   * @returns True if request is allowed
   */
  static checkRateLimit(key: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    if (!SecurityUtils.rateLimitStore) {
      SecurityUtils.rateLimitStore = new Map();
    }

    const now = Date.now();
    const windowStart = now - windowMs;

    // Get or create request history for this key
    let requests = SecurityUtils.rateLimitStore.get(key) || [];

    // Remove old requests outside the window
    requests = requests.filter(req => req.timestamp > windowStart);

    // Check if limit exceeded
    if (requests.length >= maxRequests) {
      return false;
    }

    // Add current request
    requests.push({ timestamp: now });
    SecurityUtils.rateLimitStore.set(key, requests);

    return true;
  }

  /**
   * Clean up old rate limit entries
   */
  static cleanupRateLimit(): void {
    if (!SecurityUtils.rateLimitStore) {return;}

    const now = Date.now();
    const maxAge = 300000; // 5 minutes

    for (const [key, requests] of SecurityUtils.rateLimitStore.entries()) {
      const validRequests = requests.filter(req => now - req.timestamp < maxAge);
      if (validRequests.length === 0) {
        SecurityUtils.rateLimitStore.delete(key);
      } else {
        SecurityUtils.rateLimitStore.set(key, validRequests);
      }
    }
  }

  /**
   * Validate script ID
   * @param scriptId - Script ID to validate
   * @returns True if valid
   */
  static validateScriptId(scriptId: unknown): boolean {
    if (!scriptId || typeof scriptId !== 'string') {return false;}
    
    // Allow alphanumeric, hyphens, underscores
    return /^[a-zA-Z0-9_-]+$/.test(scriptId) && scriptId.length <= 50;
  }

  /**
   * Validate voice name
   * @param voice - Voice name to validate
   * @returns True if valid
   */
  static validateVoice(voice: unknown): boolean {
    if (!voice || typeof voice !== 'string') {return false;}
    
    const validVoices = [
      'Aoede', 'Puck', 'Charon', 'Kore', 'Fenrir', 
      'Leda', 'Orus', 'Zephyr'
    ];
    
    return validVoices.includes(voice);
  }

  /**
   * Validate model name
   * @param model - Model name to validate
   * @returns True if valid
   */
  static validateModel(model: unknown): boolean {
    if (!model || typeof model !== 'string') {return false;}
    
    const validModels = [
      'gemini-2.5-flash-preview-native-audio-dialog',
      'gemini-2.0-flash-live-001'
    ];
    
    return validModels.includes(model);
  }

  /**
   * Sanitize call SID
   * @param callSid - Call SID to sanitize
   * @returns Sanitized call SID or null if invalid
   */
  static sanitizeCallSid(callSid: unknown): string | null {
    if (!callSid || typeof callSid !== 'string') {return null;}
    
    // Twilio Call SIDs start with CA and are 34 characters long
    if (!/^CA[a-f0-9]{32}$/.test(callSid)) {return null;}
    
    return callSid;
  }

  /**
   * Validate audio settings
   * @param settings - Audio settings to validate
   * @returns Validated settings or null if invalid
   */
  static validateAudioSettings(settings: unknown): Record<string, any> | null {
    if (!settings || typeof settings !== 'object' || settings === null) {return null;}
    
    const validatedSettings: Record<string, any> = {};
    const settingsObj = settings as Record<string, any>;
    
    // Boolean settings
    const booleanFields = ['deEssingEnabled', 'enableNoiseReduction', 'enableCompression', 'enableAGC'];
    for (const field of booleanFields) {
      if (field in settingsObj && typeof settingsObj[field] === 'boolean') {
        validatedSettings[field] = settingsObj[field];
      }
    }
    
    // Numeric settings with ranges
    const numericFields: Record<string, { min: number; max: number }> = {
      compressionRatio: { min: 1, max: 10 },
      noiseThreshold: { min: 0, max: 1 },
      agcTargetLevel: { min: 0.1, max: 1 }
    };
    
    for (const [field, range] of Object.entries(numericFields)) {
      if (field in settingsObj && typeof settingsObj[field] === 'number') {
        const value = Math.max(range.min, Math.min(range.max, settingsObj[field]));
        validatedSettings[field] = value;
      }
    }
    
    return Object.keys(validatedSettings).length > 0 ? validatedSettings : null;
  }

  /**
   * Generate secure session ID
   * @returns Secure session ID
   */
  static generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substring(2, 15);
    return `session_${timestamp}_${randomPart}`;
  }

  /**
   * Validate request origin for CORS
   * @param origin - Request origin
   * @param allowedOrigins - Array of allowed origins
   * @returns True if origin is allowed
   */
  static validateOrigin(origin: string | undefined, allowedOrigins: string[] = []): boolean {
    if (!origin) {return true;} // Allow requests with no origin (e.g., Twilio webhooks)
    
    try {
      const requestOrigin = new URL(origin).origin;
      return allowedOrigins.includes(requestOrigin);
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if IP is in allowed range (basic implementation)
   * @param ip - IP address to check
   * @param allowedRanges - Array of allowed IP ranges/addresses
   * @returns True if IP is allowed
   */
  static validateIP(ip: string | undefined, allowedRanges: string[] = []): boolean {
    if (!ip || allowedRanges.length === 0) {return true;}
    
    // Basic implementation - exact match only
    // In production, you'd want proper CIDR range checking
    return allowedRanges.includes(ip);
  }

  /**
   * Escape HTML to prevent XSS
   * @param text - Text to escape
   * @returns Escaped text
   */
  static escapeHtml(text: unknown): string {
    if (!text || typeof text !== 'string') {return '';}
    
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }

  /**
   * Get rate limit status for a key
   * @param key - Rate limit key
   * @param windowMs - Time window in milliseconds
   * @returns Rate limit status
   */
  static getRateLimitStatus(key: string, windowMs: number = 60000): {
    requests: number;
    resetTime: number | null;
  } {
    if (!SecurityUtils.rateLimitStore) {
      return { requests: 0, resetTime: null };
    }

    const now = Date.now();
    const windowStart = now - windowMs;
    const requests = SecurityUtils.rateLimitStore.get(key) || [];
    const validRequests = requests.filter(req => req.timestamp > windowStart);
    
    return {
      requests: validRequests.length,
      resetTime: validRequests.length > 0 ? validRequests[0].timestamp + windowMs : null
    };
  }
}

// Clean up rate limit store every 5 minutes
setInterval(() => SecurityUtils.cleanupRateLimit(), 300000);