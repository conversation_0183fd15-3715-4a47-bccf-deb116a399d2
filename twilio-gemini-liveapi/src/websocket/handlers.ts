import { TranscriptionManager } from '../audio/transcription-manager';
import { handleTwilioFlow } from './twilio-flow-handler';
import { handleLocalTestingFlow } from './local-testing-handler';
import {
    getOutboundCallConfig,
    getInboundCallConfig,
    getOutboundTestConfig,
    getInboundTestConfig
} from './config-handlers';
import type { WebSocketConnection, WebSocketDependencies, FlowDependencies } from '../types/websocket';
import type { FastifyInstance } from 'fastify';
import { authLogger, websocketLogger } from '../utils/logger';
import {
    registerMultipleWebSocketEndpoints,
    createStandardEndpointConfigs,
    validateWebSocketAuth,
    handleAuthFailure,
    logWebSocketConnection
} from './websocket-helpers';



// WebSocket handlers for different connection types
export function registerWebSocketHandlers(fastify: FastifyInstance, dependencies: WebSocketDependencies): void {
    // Initialize global transcription manager
    const transcriptionManager = new TranscriptionManager();

    // Add transcription manager to dependencies for consistency
    const enhancedDependencies: WebSocketDependencies = {
        ...dependencies,
        transcriptionManager
    };

    // Register all WebSocket endpoints using helper functions
    const endpointConfigs = createStandardEndpointConfigs({
        handleOutboundCall,
        handleInboundCall,
        handleOutboundTesting,
        handleInboundTesting
    });

    registerMultipleWebSocketEndpoints(fastify, endpointConfigs, enhancedDependencies);


}

// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    logWebSocketConnection('outbound', deps.isTestMode || false);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'outbound_call',
        getSessionConfig: (callSid?: string) => getOutboundCallConfig(deps, callSid),
        isIncomingCall: false
    });
}

// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    logWebSocketConnection('inbound', deps.isTestMode || false);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'inbound_call',
        getSessionConfig: (callSid?: string) => getInboundCallConfig(deps, callSid),
        isIncomingCall: true
    });
}

// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;

    logWebSocketConnection('outbound', true, 'testing mode');

    if (enableDetailedLogging) {
        websocketLogger.debug(`[OUTBOUND TEST] Connection object: ${!!connection}`);
        websocketLogger.debug(`[OUTBOUND TEST] Connection socket: ${!!connection.socket}`);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'outbound_test',
        getSessionConfig: (callSid?: string) => getOutboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: false
    });
}

// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;

    logWebSocketConnection('inbound', true, 'testing mode');

    if (enableDetailedLogging) {
        websocketLogger.debug(`[INBOUND TEST] Connection details logged`);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'inbound_test',
        getSessionConfig: (callSid?: string) => getInboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: true
    });
}