// Enhanced audio buffer validation and error handling
import { websocketLogger, audioLogger } from '../utils/logger';

export interface AudioValidationResult {
    isValid: boolean;
    error?: string;
    warnings?: string[];
    metadata?: AudioBufferMetadata;
}

export interface AudioBufferMetadata {
    size: number;
    format: string;
    sampleRate?: number;
    channels?: number;
    duration?: number;
    isEmpty: boolean;
    hasClipping: boolean;
    hasSilence: boolean;
    qualityScore: number;
}

export interface AudioValidationConfig {
    maxBufferSize: number;
    minBufferSize: number;
    allowedFormats: string[];
    maxSilencePercentage: number;
    maxClippingPercentage: number;
    enableDetailedAnalysis: boolean;
}

/**
 * Enhanced audio buffer validator with comprehensive error handling
 */
export class AudioBufferValidator {
    private config: AudioValidationConfig;
    private validationStats: Map<string, number> = new Map();

    constructor(config: Partial<AudioValidationConfig> = {}) {
        this.config = {
            maxBufferSize: 10 * 1024 * 1024, // 10MB
            minBufferSize: 1, // 1 byte minimum
            allowedFormats: ['ulaw', 'pcm16', 'pcm32', 'float32'],
            maxSilencePercentage: 95, // 95% silence threshold
            maxClippingPercentage: 10, // 10% clipping threshold
            enableDetailedAnalysis: true,
            ...config
        };
    }

    /**
     * Comprehensive audio buffer validation
     */
    validateAudioBuffer(
        buffer: unknown, 
        format: string = 'ulaw',
        sessionId?: string
    ): AudioValidationResult {
        const validationId = sessionId || 'unknown';
        
        try {
            // Basic type validation
            const basicValidation = this.validateBasicType(buffer);
            if (!basicValidation.isValid) {
                this.incrementValidationStat('type_errors');
                return basicValidation;
            }

            const audioBuffer = buffer as Buffer;

            // Size validation
            const sizeValidation = this.validateBufferSize(audioBuffer, validationId);
            if (!sizeValidation.isValid) {
                this.incrementValidationStat('size_errors');
                return sizeValidation;
            }

            // Format validation
            const formatValidation = this.validateFormat(format);
            if (!formatValidation.isValid) {
                this.incrementValidationStat('format_errors');
                return formatValidation;
            }

            // Content analysis
            const metadata = this.analyzeBufferContent(audioBuffer, format);
            const contentValidation = this.validateContent(metadata, validationId);

            if (!contentValidation.isValid) {
                this.incrementValidationStat('content_errors');
                return {
                    ...contentValidation,
                    metadata
                };
            }

            this.incrementValidationStat('successful_validations');
            
            return {
                isValid: true,
                metadata,
                warnings: contentValidation.warnings
            };

        } catch (error) {
            this.incrementValidationStat('validation_exceptions');
            websocketLogger.error('Audio validation exception', error instanceof Error ? error : new Error(String(error)));
            
            return {
                isValid: false,
                error: `Validation exception: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    /**
     * Validate basic buffer type and existence
     */
    private validateBasicType(buffer: unknown): AudioValidationResult {
        if (buffer === null || buffer === undefined) {
            return {
                isValid: false,
                error: 'Audio buffer is null or undefined'
            };
        }

        if (!Buffer.isBuffer(buffer)) {
            return {
                isValid: false,
                error: `Invalid buffer type: expected Buffer, got ${typeof buffer}`
            };
        }

        return { isValid: true };
    }

    /**
     * Validate buffer size constraints
     */
    private validateBufferSize(buffer: Buffer, sessionId: string): AudioValidationResult {
        if (buffer.length === 0) {
            return {
                isValid: false,
                error: 'Audio buffer is empty (0 bytes)'
            };
        }

        if (buffer.length < this.config.minBufferSize) {
            return {
                isValid: false,
                error: `Buffer too small: ${buffer.length} bytes (minimum: ${this.config.minBufferSize})`
            };
        }

        if (buffer.length > this.config.maxBufferSize) {
            websocketLogger.warn(`Large audio buffer detected: ${buffer.length} bytes`, { sessionId });
            return {
                isValid: false,
                error: `Buffer too large: ${buffer.length} bytes (maximum: ${this.config.maxBufferSize})`
            };
        }

        return { isValid: true };
    }

    /**
     * Validate audio format
     */
    private validateFormat(format: string): AudioValidationResult {
        if (!format || typeof format !== 'string') {
            return {
                isValid: false,
                error: 'Audio format must be a non-empty string'
            };
        }

        const normalizedFormat = format.toLowerCase().trim();
        if (!this.config.allowedFormats.includes(normalizedFormat)) {
            return {
                isValid: false,
                error: `Unsupported audio format: ${format} (supported: ${this.config.allowedFormats.join(', ')})`
            };
        }

        return { isValid: true };
    }

    /**
     * Analyze buffer content for quality metrics
     */
    private analyzeBufferContent(buffer: Buffer, format: string): AudioBufferMetadata {
        const metadata: AudioBufferMetadata = {
            size: buffer.length,
            format: format.toLowerCase(),
            isEmpty: buffer.length === 0,
            hasClipping: false,
            hasSilence: false,
            qualityScore: 100
        };

        if (!this.config.enableDetailedAnalysis || buffer.length === 0) {
            return metadata;
        }

        try {
            // Analyze based on format
            if (format.toLowerCase() === 'ulaw') {
                this.analyzeUlawContent(buffer, metadata);
            } else if (format.toLowerCase().includes('pcm')) {
                this.analyzePcmContent(buffer, metadata);
            }

            // Calculate overall quality score
            metadata.qualityScore = this.calculateQualityScore(metadata);

        } catch (error) {
            websocketLogger.warn('Error during audio content analysis', error instanceof Error ? error : new Error(String(error)));
            metadata.qualityScore = 50; // Default to medium quality on analysis error
        }

        return metadata;
    }

    /**
     * Analyze μ-law audio content
     */
    private analyzeUlawContent(buffer: Buffer, metadata: AudioBufferMetadata): void {
        let silentSamples = 0;
        let clippedSamples = 0;
        const totalSamples = buffer.length;

        for (let i = 0; i < buffer.length; i++) {
            const sample = buffer[i];
            
            // Check for silence (μ-law 0x7F is silence)
            if (sample === 0x7F || sample === 0xFF) {
                silentSamples++;
            }
            
            // Check for clipping (extreme values)
            if (sample === 0x00 || sample === 0x80) {
                clippedSamples++;
            }
        }

        const silencePercentage = (silentSamples / totalSamples) * 100;
        const clippingPercentage = (clippedSamples / totalSamples) * 100;

        metadata.hasSilence = silencePercentage > this.config.maxSilencePercentage;
        metadata.hasClipping = clippingPercentage > this.config.maxClippingPercentage;
    }

    /**
     * Analyze PCM audio content
     */
    private analyzePcmContent(buffer: Buffer, metadata: AudioBufferMetadata): void {
        const sampleSize = metadata.format.includes('32') ? 4 : 2;
        const totalSamples = Math.floor(buffer.length / sampleSize);
        let silentSamples = 0;
        let clippedSamples = 0;

        for (let i = 0; i < totalSamples; i++) {
            const offset = i * sampleSize;
            let sample: number;

            if (sampleSize === 2) {
                sample = buffer.readInt16LE(offset);
                // Check for clipping (near max/min values)
                if (Math.abs(sample) > 30000) {clippedSamples++;}
                // Check for silence
                if (Math.abs(sample) < 100) {silentSamples++;}
            } else {
                sample = buffer.readInt32LE(offset);
                // Check for clipping
                if (Math.abs(sample) > 2000000000) {clippedSamples++;}
                // Check for silence
                if (Math.abs(sample) < 10000) {silentSamples++;}
            }
        }

        const silencePercentage = (silentSamples / totalSamples) * 100;
        const clippingPercentage = (clippedSamples / totalSamples) * 100;

        metadata.hasSilence = silencePercentage > this.config.maxSilencePercentage;
        metadata.hasClipping = clippingPercentage > this.config.maxClippingPercentage;
    }

    /**
     * Calculate overall quality score
     */
    private calculateQualityScore(metadata: AudioBufferMetadata): number {
        let score = 100;

        if (metadata.hasClipping) {score -= 30;}
        if (metadata.hasSilence) {score -= 20;}
        if (metadata.size < 100) {score -= 25;} // Very small buffers
        if (metadata.size > 1024 * 1024) {score -= 10;} // Very large buffers

        return Math.max(0, Math.min(100, score));
    }

    /**
     * Validate content based on metadata
     */
    private validateContent(metadata: AudioBufferMetadata, sessionId: string): AudioValidationResult {
        const warnings: string[] = [];

        if (metadata.hasClipping) {
            warnings.push('Audio contains clipping - may indicate input overload');
        }

        if (metadata.hasSilence) {
            warnings.push('Audio contains excessive silence - may indicate input issues');
        }

        if (metadata.qualityScore < 50) {
            return {
                isValid: false,
                error: `Poor audio quality detected (score: ${metadata.qualityScore}/100)`,
                warnings
            };
        }

        if (metadata.qualityScore < 70) {
            warnings.push(`Low audio quality detected (score: ${metadata.qualityScore}/100)`);
        }

        return {
            isValid: true,
            warnings: warnings.length > 0 ? warnings : undefined
        };
    }

    /**
     * Increment validation statistics
     */
    private incrementValidationStat(stat: string): void {
        const current = this.validationStats.get(stat) || 0;
        this.validationStats.set(stat, current + 1);
    }

    /**
     * Get validation statistics
     */
    getValidationStats(): Record<string, number> {
        const stats: Record<string, number> = {};
        for (const [key, value] of this.validationStats.entries()) {
            stats[key] = value;
        }
        return stats;
    }

    /**
     * Reset validation statistics
     */
    resetStats(): void {
        this.validationStats.clear();
    }
}
export interface AudioQualityMetrics {
    totalSamples: number;
    clippedSamples: number;
    silentSamples: number;
    peakLevel: number;
    averageLevel: number;
    dynamicRange: number;
    lastUpdate: number;
}

export interface AudioMetrics {
    peak: number;
    rms: number;
    silencePercentage: number;
    clippingPercentage: number;
    dynamicRange: number;
    qualityScore: number;
    sampleCount?: number;
}

export interface AudioQualitySummary {
    totalAnalyzed: number;
    averageQuality: number;
    clippingRate: number;
    silenceRate: number;
    averageDynamicRange: number;
    lastAnalysis: number;
    totalSamples?: number;
}

export const audioQualityMonitor = {
        metrics: {
            totalSamples: 0,
            clippedSamples: 0,
            silentSamples: 0,
            peakLevel: 0,
            averageLevel: 0,
            dynamicRange: 0,
            lastUpdate: Date.now()
        } as AudioQualityMetrics,

        /**
         * Analyze audio quality metrics
         * @param samples - Float32Array of audio samples
         * @param label - Label for logging (e.g., 'input', 'output')
         */
        analyze(samples: Float32Array, label = 'unknown'): AudioMetrics | null {
            try {
                const metrics = this.calculateMetrics(samples);
                this.updateGlobalMetrics(metrics);

                if (process.env.AUDIO_DEBUG === 'true') {
                    audioLogger.debug(`[${label}] Audio Quality Metrics`, {
                        samples: samples.length,
                        peak: metrics.peak.toFixed(3),
                        rms: metrics.rms.toFixed(3),
                        clipping: metrics.clippingPercentage.toFixed(1) + '%',
                        silence: metrics.silencePercentage.toFixed(1) + '%',
                        dynamicRange: metrics.dynamicRange.toFixed(1) + 'dB'
                    });
                }

                // Warn about quality issues
                if (metrics.clippingPercentage > 5) {
                    audioLogger.warn(`[${label}] High clipping detected: ${metrics.clippingPercentage.toFixed(1)}%`);
                }
                if (metrics.silencePercentage > 80) {
                    audioLogger.warn(`[${label}] Mostly silent audio: ${metrics.silencePercentage.toFixed(1)}%`);
                }
                if (metrics.dynamicRange < 20) {
                    audioLogger.warn(`[${label}] Low dynamic range: ${metrics.dynamicRange.toFixed(1)}dB`);
                }

                return metrics;
            } catch (error) {
                audioLogger.error(`Error analyzing audio quality for ${label}`, error as Error);
                return null;
            }
        },

        /**
         * Calculate detailed audio metrics
         * @param samples - Float32Array of audio samples
         * @returns Object with audio quality metrics
         */
        calculateMetrics(samples: Float32Array): AudioMetrics {
            let peak = 0;
            let rmsSum = 0;
            let clippedCount = 0;
            let silentCount = 0;
            let min = Infinity;
            let max = -Infinity;

            const silenceThreshold = 0.001;
            const clippingThreshold = 0.95;

            for (let i = 0; i < samples.length; i++) {
                const sample = samples[i];
                const absSample = Math.abs(sample);

                // Peak detection
                if (absSample > peak) {
                    peak = absSample;
                }

                // RMS calculation
                rmsSum += sample * sample;

                // Clipping detection
                if (absSample > clippingThreshold) {
                    clippedCount++;
                }

                // Silence detection
                if (absSample < silenceThreshold) {
                    silentCount++;
                }

                // Min/max for dynamic range
                if (sample < min) {
                    min = sample;
                }
                if (sample > max) {
                    max = sample;
                }
            }

            const rms = Math.sqrt(rmsSum / samples.length);
            const dynamicRange = 20 * Math.log10((max - min) / 2);

            return {
                peak,
                rms,
                clippingPercentage: (clippedCount / samples.length) * 100,
                silencePercentage: (silentCount / samples.length) * 100,
                dynamicRange: isFinite(dynamicRange) ? dynamicRange : 0,
                qualityScore: 0, // Will be calculated elsewhere
                sampleCount: samples.length
            };
        },

        /**
         * Update global metrics tracking
         * @param metrics - Current metrics object
         */
        updateGlobalMetrics(metrics: AudioMetrics): void {
            const sampleCount = metrics.sampleCount || 0;
            this.metrics.totalSamples += sampleCount;
            this.metrics.clippedSamples += (metrics.clippingPercentage / 100) * sampleCount;
            this.metrics.silentSamples += (metrics.silencePercentage / 100) * sampleCount;

            if (metrics.peak > this.metrics.peakLevel) {
                this.metrics.peakLevel = metrics.peak;
            }

            // Running average of RMS level
            const alpha = 0.1; // Smoothing factor
            this.metrics.averageLevel = this.metrics.averageLevel * (1 - alpha) + metrics.rms * alpha;

            this.metrics.dynamicRange = metrics.dynamicRange;
            this.metrics.lastUpdate = Date.now();
        },

        /**
         * Get current quality summary
         * @returns Object with quality summary
         */
        getSummary(): AudioQualitySummary {
            const totalClippingPercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.clippedSamples / this.metrics.totalSamples) * 100 : 0;
            const totalSilencePercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.silentSamples / this.metrics.totalSamples) * 100 : 0;

            return {
                totalAnalyzed: this.metrics.totalSamples,
                averageQuality: this.metrics.averageLevel,
                clippingRate: totalClippingPercentage,
                silenceRate: totalSilencePercentage,
                averageDynamicRange: this.metrics.dynamicRange,
                lastAnalysis: this.metrics.lastUpdate,
                totalSamples: this.metrics.totalSamples
            };
        },

        /**
         * Reset metrics
         */
        reset(): void {
            this.metrics = {
                totalSamples: 0,
                clippedSamples: 0,
                silentSamples: 0,
                peakLevel: 0,
                averageLevel: 0,
                dynamicRange: 0,
                lastUpdate: Date.now()
            };
        }
    };

