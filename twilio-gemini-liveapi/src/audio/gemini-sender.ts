import { ConnectionData } from '../types/global';
import { sessionLogger } from '../utils/logger';

/**
 * Send raw PCM audio to Gemini using connection data.
 * The buffer is converted to base64 before sending.
 */
export async function sendAudioBufferToGemini(
  callSid: string,
  connectionData: ConnectionData | undefined,
  pcmBuffer: Buffer,
  mimeType = 'audio/pcm;rate=16000'
): Promise<void> {
  try {
    if (!connectionData || !connectionData.geminiSession) {
      sessionLogger.warn(`⚠️ [${callSid}] No Gemini session available for audio send`);
      return;
    }

    const base64Audio = pcmBuffer.toString('base64');
    
    // Use sendRealtimeInput for audio data
    await connectionData.geminiSession.sendRealtimeInput({
      media: {
        data: base64Audio,
        mimeType: mimeType
      }
    });
    sessionLogger.info(`✅ [${callSid}] Audio sent to Gemini successfully`);
  } catch (error) {
    sessionLogger.error(
      `❌ [${callSid}] Error sending audio to Gemini`,
      error instanceof Error ? error : new Error(String(error))
    );
  }
}
