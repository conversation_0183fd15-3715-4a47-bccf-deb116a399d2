/**
 * Audio Forwarding Utilities
 * Handles proper audio forwarding with sequence numbers for Twilio calls
 * Fixes the missing audio forwarding issues identified in the audit
 */

import { getTwilioWebSocket, getLocalWebSocket, safeSendWebSocket, getWebSocketState } from '../utils/websocket-utils';
import { audioLogger } from '../utils/logger';
import { AudioProcessor } from './audio-processor';
import { ConnectionData } from '../types/global';

// Audio buffering for temporary WebSocket unavailability
const audioBuffer = new Map<string, Array<{ audio: AudioData; timestamp: number; retryCount: number }>>();
const MAX_BUFFER_SIZE = 10;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_MS = 1000;

// Thread-safe operations for audio buffer
const audioBufferLocks = new Map<string, Promise<void>>();

// Type definitions
interface AudioData {
    data: string;
    mimeType?: string;
}

interface AudioMessage {
    type: string;
    audio: string;
    mimeType: string;
    timestamp: number;
    sessionId: string;
}

import type { TwilioMediaMessage } from '../types/websocket';

interface AudioForwardingStats {
    sessionId: string;
    sessionType?: string;
    audioForwardingEnabled: boolean;
    lastAudioSent: number;
    timeSinceLastAudio: number | null;
    sequenceNumber?: number;
    streamSid?: string | null;
    hasTwilioWs?: boolean;
    hasLocalWs?: boolean;
}

/**
 * Forward audio to Twilio WebSocket with proper sequence number handling
 */
export async function forwardAudioToTwilio(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<boolean> {
    try {
        if (!audio || !audio.data) {
            audioLogger.warn('🚫 No audio data to forward to Twilio', {
                hasAudio: !!audio,
                hasAudioData: !!(audio?.data)
            }, sessionId);
            return false;
        }

        const twilioWs = getTwilioWebSocket(connectionData);
        const wsState = twilioWs ? getWebSocketState(twilioWs) : 'null';
        
        audioLogger.info('📞 Attempting Twilio audio forwarding', {
            hasTwilioWs: !!twilioWs,
            wsState: wsState,
            hasStreamSid: !!connectionData.streamSid,
            sequenceNumber: connectionData.sequenceNumber,
            audioDataLength: audio.data.length
        }, sessionId);

        if (!twilioWs) {
            audioLogger.error('❌ No Twilio WebSocket available for audio forwarding', {
                wsConnections: {
                    twilioWs: !!connectionData.twilioWs,
                    generalWs: !!connectionData.ws,
                    hasAnyWs: !!(connectionData.twilioWs || connectionData.ws)
                }
            }, sessionId);
            return false;
        }

        if (wsState !== 'OPEN') {
            audioLogger.error('❌ Twilio WebSocket not in OPEN state', {
                currentState: wsState,
                readyState: twilioWs.readyState
            }, sessionId);
            return false;
        }

        if (!connectionData.streamSid) {
            audioLogger.error('❌ No streamSid available for Twilio audio forwarding', {
                hasStreamSid: !!connectionData.streamSid,
                connectionKeys: Object.keys(connectionData)
            }, sessionId);
            return false;
        }

        // Convert Gemini PCM audio to μ-law format for Twilio
        // Gemini returns base64-encoded PCM audio at 24kHz, we need to convert to μ-law at 8kHz
        let convertedAudio: string | Buffer;
        let convertedAudioBase64: string;

        try {
            // Try the main conversion method first
            convertedAudio = audioProcessor?.convertPCMToUlaw?.(audio.data);

            if (!convertedAudio) {
                audioLogger.error('convertPCMToUlaw method failed', {
                    audioDataType: typeof audio.data,
                    audioDataLength: audio.data?.length || 0,
                    audioMimeType: audio.mimeType
                }, sessionId);
                return false;
            }

            // Convert Buffer to base64 string if needed for Twilio transmission
            convertedAudioBase64 = Buffer.isBuffer(convertedAudio) 
                ? convertedAudio.toString('base64') 
                : convertedAudio;

            audioLogger.debug('Audio conversion successful', {
                convertedAudioSize: convertedAudioBase64.length
            }, sessionId);

        } catch (conversionError) {
            audioLogger.error('Audio conversion error', conversionError as Error, sessionId);
            return false;
        }

        // Initialize sequence number if not exists
        if (typeof connectionData.sequenceNumber !== 'number') {
            connectionData.sequenceNumber = 0;
            audioLogger.debug('Initialized Twilio sequence number to 0', {}, sessionId);
        }

        // Create Twilio media message with sequence number
        const audioDelta: TwilioMediaMessage = {
            event: 'media',
            sequenceNumber: connectionData.sequenceNumber.toString(),
            streamSid: connectionData.streamSid,
            media: {
                payload: convertedAudioBase64
            }
        };

        // Send to Twilio WebSocket
        const success = safeSendWebSocket(twilioWs, audioDelta, sessionId);
        
        if (success) {
            // Increment sequence number for next packet with overflow protection
            connectionData.sequenceNumber = (connectionData.sequenceNumber + 1) % Number.MAX_SAFE_INTEGER;
            connectionData.lastAudioSent = Date.now();
            
            audioLogger.debug('Audio sent to Twilio', {
                sequenceNumber: connectionData.sequenceNumber - 1,
                audioSize: convertedAudioBase64.length
            }, sessionId);
            
            // Remove frequent logging for better performance - only log on errors
            
            return true;
        } else {
            audioLogger.error('Failed to send audio to Twilio WebSocket', {}, sessionId);
            return false;
        }

    } catch (error) {
        audioLogger.error('Error forwarding audio to Twilio', error as Error, sessionId);
        return false;
    }
}

/**
 * Forward audio to local testing WebSocket
 */
export async function forwardAudioToLocal(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData
): Promise<boolean> {
    try {
        if (!audio || !audio.data) {
            audioLogger.warn('🚫 No audio data to forward to local WebSocket', {
                hasAudio: !!audio,
                hasAudioData: !!(audio?.data)
            }, sessionId);
            return false;
        }

        const localWs = getLocalWebSocket(connectionData);
        const wsState = localWs ? getWebSocketState(localWs) : 'null';
        
        audioLogger.info('🏠 Attempting local audio forwarding', {
            hasLocalWs: !!localWs,
            wsState: wsState,
            audioDataLength: audio.data.length,
            mimeType: audio.mimeType
        }, sessionId);

        if (!localWs) {
            audioLogger.error('❌ No local WebSocket available for audio forwarding', {
                wsConnections: {
                    localWs: !!connectionData.localWs,
                    generalWs: !!connectionData.ws,
                    hasAnyWs: !!(connectionData.localWs || connectionData.ws)
                }
            }, sessionId);
            return false;
        }

        if (wsState !== 'OPEN') {
            audioLogger.error('❌ Local WebSocket not in OPEN state', {
                currentState: wsState,
                readyState: localWs.readyState
            }, sessionId);
            return false;
        }

        // For local testing, send audio data with proper format information
        const audioMessage: AudioMessage = {
            type: 'audio',
            audio: audio.data, // Use standardized 'audio' field name
            mimeType: audio.mimeType || 'audio/pcm;rate=24000', // Include mime type for proper playback
            timestamp: Date.now(),
            sessionId: sessionId
        };

        const success = safeSendWebSocket(localWs, audioMessage, sessionId);
        
        if (success) {
            connectionData.lastAudioSent = Date.now();
            audioLogger.debug('Audio sent to local WebSocket', {
                audioSize: audio.data.length
            }, sessionId);
            return true;
        } else {
            audioLogger.error('Failed to send audio to local WebSocket', {}, sessionId);
            return false;
        }

    } catch (error) {
        audioLogger.error('Error forwarding audio to local WebSocket', error as Error, sessionId);
        return false;
    }
}

/**
 * Main audio forwarding function that routes to appropriate destination
 */
export async function forwardAudio(
    sessionId: string,
    audio: AudioData,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<boolean> {
    if (!audio || !connectionData) {
        audioLogger.warn('🚫 Missing audio or connection data for forwarding', {
            hasAudio: !!audio,
            hasConnectionData: !!connectionData,
            audioDataLength: audio?.data?.length || 0
        }, sessionId);
        return false;
    }

    // Comprehensive WebSocket state logging
    const twilioWs = getTwilioWebSocket(connectionData);
    const localWs = getLocalWebSocket(connectionData);
    const generalWs = connectionData.ws;

    audioLogger.info('🎵 Audio forwarding decision point', {
        sessionType: connectionData.sessionType,
        isTwilioCall: connectionData.isTwilioCall,
        streamSid: connectionData.streamSid,
        audioForwardingEnabled: connectionData.audioForwardingEnabled,
        hasTwilioWs: !!twilioWs,
        hasLocalWs: !!localWs,
        hasGeneralWs: !!generalWs,
        twilioWsState: twilioWs ? getWebSocketState(twilioWs) : 'null',
        localWsState: localWs ? getWebSocketState(localWs) : 'null',
        generalWsState: generalWs ? getWebSocketState(generalWs) : 'null',
        audioDataLength: audio.data.length,
        audioMimeType: audio.mimeType
    }, sessionId);

    // Determine session type and route accordingly
    const sessionType = connectionData.sessionType || 'unknown';
    const isTwilioCall = connectionData.isTwilioCall || sessionType === 'twilio_call';
    
    // Enhanced routing decision logging
    audioLogger.info('🎯 Audio routing decision', {
        sessionType,
        isTwilioCall,
        routingTo: isTwilioCall ? 'Twilio' : 'Local'
    }, sessionId);

    // Check if WebSocket is ready, if not, buffer the audio
    const wsToCheck = isTwilioCall ? getTwilioWebSocket(connectionData) : getLocalWebSocket(connectionData);
    const wsState = wsToCheck ? getWebSocketState(wsToCheck) : 'null';
    
    if (!wsToCheck || wsState !== 'OPEN') {
        audioLogger.warn(`📦 WebSocket not ready (${wsState}), buffering audio for later delivery`, {
            isTwilioCall,
            wsState,
            audioDataLength: audio.data.length
        }, sessionId);
        
        bufferAudio(sessionId, audio);
        return false; // Audio buffered, not immediately sent
    }

    // Try to process any previously buffered audio first
    await processBufferedAudio(sessionId, connectionData, audioProcessor);

    if (isTwilioCall) {
        const result = await forwardAudioToTwilio(sessionId, audio, connectionData, audioProcessor);
        audioLogger.info(`📞 Twilio audio forwarding result: ${result ? 'SUCCESS' : 'FAILED'}`, {}, sessionId);
        return result;
    } else {
        const result = await forwardAudioToLocal(sessionId, audio, connectionData);
        audioLogger.info(`🏠 Local audio forwarding result: ${result ? 'SUCCESS' : 'FAILED'}`, {}, sessionId);
        return result;
    }
}

/**
 * Initialize audio forwarding for a session
 */
export function initializeAudioForwarding(
    sessionId: string, 
    connectionData: ConnectionData, 
    sessionType: string
): void {
    if (!connectionData) {
        audioLogger.warn('No connection data for audio forwarding initialization', {}, sessionId);
        return;
    }

    // Set audio forwarding properties based on session type
    if (sessionType === 'twilio_call' || connectionData.isTwilioCall) {
        connectionData.audioForwardingEnabled = true;
        connectionData.sequenceNumber = 0;
        connectionData.lastAudioSent = 0;
        audioLogger.info('Initialized Twilio audio forwarding', {}, sessionId);
    } else {
        connectionData.audioForwardingEnabled = true;
        connectionData.lastAudioSent = 0;
        audioLogger.info('Initialized local audio forwarding', {}, sessionId);
    }
}

/**
 * Get audio forwarding statistics
 */
export function getAudioForwardingStats(
    sessionId: string, 
    connectionData: ConnectionData
): AudioForwardingStats | null {
    if (!connectionData) {
        return null;
    }

    const stats: AudioForwardingStats = {
        sessionId,
        sessionType: connectionData.sessionType,
        audioForwardingEnabled: connectionData.audioForwardingEnabled || false,
        lastAudioSent: connectionData.lastAudioSent || 0,
        timeSinceLastAudio: connectionData.lastAudioSent ? Date.now() - connectionData.lastAudioSent : null
    };

    // Add Twilio-specific stats
    if (connectionData.isTwilioCall || connectionData.sessionType === 'twilio_call') {
        stats.sequenceNumber = connectionData.sequenceNumber || 0;
        stats.streamSid = connectionData.streamSid || null;
        stats.hasTwilioWs = !!(connectionData.twilioWs || connectionData.ws);
    } else {
        stats.hasLocalWs = !!(connectionData.localWs || connectionData.ws);
    }

    return stats;
}

/**
 * Cleanup audio forwarding resources
 */
export function cleanupAudioForwarding(
    sessionId: string, 
    connectionData: ConnectionData
): void {
    if (!connectionData) {
        return;
    }

    // Reset audio forwarding state
    connectionData.audioForwardingEnabled = false;
    connectionData.sequenceNumber = 0;
    connectionData.lastAudioSent = 0;
    
    // Clean up audio buffer for this session
    cleanupAudioBuffer(sessionId);
    
    audioLogger.debug('Audio forwarding cleanup completed', {}, sessionId);
}

/**
 * Buffer audio when WebSocket is not ready (thread-safe)
 */
function bufferAudio(sessionId: string, audio: AudioData): void {
    // Use atomic operation to prevent race conditions
    const lockKey = `audio_buffer_${sessionId}`;
    const currentLock = audioBufferLocks.get(lockKey) || Promise.resolve();

    const newLock = currentLock.then(() => {
        if (!audioBuffer.has(sessionId)) {
            audioBuffer.set(sessionId, []);
        }

        const buffer = audioBuffer.get(sessionId)!;

        // Remove oldest entries if buffer is full
        while (buffer.length >= MAX_BUFFER_SIZE) {
            const dropped = buffer.shift();
            audioLogger.warn('🗑️ Dropped oldest buffered audio packet', {
                droppedTimestamp: dropped?.timestamp,
                bufferSize: buffer.length
            }, sessionId);
        }

        buffer.push({
            audio,
            timestamp: Date.now(),
            retryCount: 0
        });

        audioLogger.info('📦 Audio buffered for later delivery', {
            bufferSize: buffer.length,
            audioDataLength: audio.data.length
        }, sessionId);
    }).catch(error => {
        audioLogger.error('❌ Error in bufferAudio:', error as Error, sessionId);
    });

    audioBufferLocks.set(lockKey, newLock);

    // Clean up completed locks to prevent memory leaks
    newLock.finally(() => {
        if (audioBufferLocks.get(lockKey) === newLock) {
            audioBufferLocks.delete(lockKey);
        }
    });
}

/**
 * Process buffered audio when WebSocket becomes available (thread-safe)
 */
async function processBufferedAudio(
    sessionId: string,
    connectionData: ConnectionData,
    audioProcessor: AudioProcessor
): Promise<void> {
    // Use the same locking mechanism to prevent race conditions
    const lockKey = `audio_buffer_${sessionId}`;
    const currentLock = audioBufferLocks.get(lockKey) || Promise.resolve();

    const processLock = currentLock.then(async () => {
        const buffer = audioBuffer.get(sessionId);
        if (!buffer || buffer.length === 0) {
            return;
        }

        audioLogger.info('🔄 Processing buffered audio', {
            bufferSize: buffer.length
        }, sessionId);

        // Create a copy of items to process and clear the original buffer
        const itemsToProcess = [...buffer];
        buffer.length = 0; // Clear buffer

        for (const item of itemsToProcess) {
            try {
                const success = await forwardAudio(sessionId, item.audio, connectionData, audioProcessor);
                if (!success) {
                    // If still failing, put back in buffer with increased retry count
                    if (item.retryCount < MAX_RETRY_ATTEMPTS) {
                        item.retryCount++;
                        buffer.push(item);
                        audioLogger.warn('⏳ Re-buffered audio for retry', {
                            retryCount: item.retryCount,
                            maxRetries: MAX_RETRY_ATTEMPTS
                        }, sessionId);
                    } else {
                        audioLogger.error('❌ Audio packet dropped after max retries', {
                            retryCount: item.retryCount,
                            ageMs: Date.now() - item.timestamp
                        }, sessionId);
                    }
                }
            } catch (error) {
                audioLogger.error('❌ Error processing buffered audio', error as Error, sessionId);
            }
        }
    }).catch(error => {
        audioLogger.error('❌ Error in processBufferedAudio:', error as Error, sessionId);
    });

    audioBufferLocks.set(lockKey, processLock);

    // Clean up completed locks to prevent memory leaks
    processLock.finally(() => {
        if (audioBufferLocks.get(lockKey) === processLock) {
            audioBufferLocks.delete(lockKey);
        }
    });
}

/**
 * Clean up audio buffer for a session (thread-safe)
 */
export function cleanupAudioBuffer(sessionId: string): void {
    // Use the same locking mechanism to prevent race conditions during cleanup
    const lockKey = `audio_buffer_${sessionId}`;
    const currentLock = audioBufferLocks.get(lockKey) || Promise.resolve();

    const cleanupLock = currentLock.then(() => {
        const buffer = audioBuffer.get(sessionId);
        if (buffer) {
            audioLogger.info('🧹 Cleaning up audio buffer', {
                bufferedPackets: buffer.length
            }, sessionId);
            audioBuffer.delete(sessionId);
        }
    }).catch(error => {
        audioLogger.error('❌ Error in cleanupAudioBuffer:', error as Error, sessionId);
    });

    audioBufferLocks.set(lockKey, cleanupLock);

    // Clean up completed locks to prevent memory leaks
    cleanupLock.finally(() => {
        if (audioBufferLocks.get(lockKey) === cleanupLock) {
            audioBufferLocks.delete(lockKey);
        }
    });
}