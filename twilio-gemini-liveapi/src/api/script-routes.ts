// Script management routes for the Twilio Gemini service
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { SecurityUtils } from '../middleware/security-utils';
import { loadCampaignScript } from '../scripts/campaign-loader';
import {
    Dependencies,
    CampaignScriptParams
} from '../types/api-types';

// Type definitions for script routes
interface IncomingCampaignParams {
    id: string;
}

interface ScriptIdParams {
    scriptId: string;
}

interface ConfigureIncomingScenarioBody {
    scenarioId?: string;
    name?: string;
    language?: string;
    fromNumber?: string;
    voice?: string;
    model?: string;
    country?: string;
    script?: string;
    isActive?: boolean;
}

interface SelectScenarioBody {
    scenarioId: string;
}

export function registerScriptRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('📋 Starting script route registration...');
    
    const { scriptManager } = dependencies;
    
    if (!scriptManager) {
        console.error('❌ Script manager not available - script routes will not work properly');
    }

    // Campaign script endpoints (UI compatibility)

    // Get campaign script by ID (for UI compatibility)
    fastify.get<{ Params: CampaignScriptParams }>('/get-campaign-script/:id', async (request, reply) => {
        try {
            const { id } = request.params;

            if (!SecurityUtils.validateScriptId(id)) {
                reply.status(400);
                return { error: 'Invalid script ID' };
            }

            // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
            const numericId = parseInt(id);
            
            // Check if parseInt returned a valid number
            if (isNaN(numericId)) {
                return {
                    success: false,
                    error: 'Invalid campaign ID format. Must be a number.'
                };
            }
            
            let campaignScript = null;

            if (numericId >= 7 && numericId <= 12) {
                // IDs 7-12 are incoming campaigns (map to incoming-campaign1.json to incoming-campaign6.json)
                const incomingCampaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
                campaignScript = loadCampaignScript(incomingCampaignId, 'incoming', false);
                console.log(`✅ Loading incoming campaign ${incomingCampaignId} for ID ${id}`);
            } else if (numericId >= 1 && numericId <= 6) {
                // IDs 1-6 are outbound campaigns (map to campaign1.json to campaign6.json)
                campaignScript = loadCampaignScript(numericId, 'outbound', false);
                console.log(`✅ Loading outbound campaign ${numericId} for ID ${id}`);
            }

            if (campaignScript) {
                // Return the campaign script directly in the format expected by the frontend
                return campaignScript;
            } else {
                reply.status(404);
                return { error: `Campaign script ${id} not found` };
            }
        } catch (error) {
            console.error('❌ Error getting campaign script:', error);
            reply.status(500);
            return { error: (error as Error).message };
        }
    });

    // Get incoming campaign script (alternative route for UI compatibility)
    fastify.get<{ Params: IncomingCampaignParams }>('/incoming-campaign/:id', async (request, reply) => {
        try {
            const id = request.params.id.replace('.json', ''); // Remove .json if present
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Return the script as-is without any conversion
                return script;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Get incoming campaign script (for UI compatibility)
    fastify.get<{ Params: IncomingCampaignParams }>('/incoming-campaign\\::id.json', async (request, reply) => {
        try {
            const id = request.params.id;
            console.log('📥 [DEBUG] Incoming campaign script request for ID:', id);

            if (!id) {
                console.log('❌ [DEBUG] No ID provided');
                reply.status(400);
                return { success: false, error: 'Script ID is required' };
            }

            if (!SecurityUtils.validateScriptId(id)) {
                console.log('❌ [DEBUG] Invalid script ID:', id);
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const script = await scriptManager.getScriptConfig(id, true);
            console.log('📊 [DEBUG] Script found:', !!script);

            if (script) {
                // Convert to the format expected by the frontend - no hardcoded instructions
                const campaignData = {
                    campaign: script.scriptId || id,
                    agentPersona: {
                        name: 'AI Assistant',
                        tone: '' // Campaign script should define tone
                    },
                    script: {
                        instructions: script.aiInstructions || '' // Campaign script should provide all instructions
                    },
                    customerData: {
                        handling: '' // Campaign script should define handling
                    },
                    transferData: {
                        protocols: '' // Campaign script should define protocols
                    }
                };

                return campaignData;
            } else {
                console.log('❌ [DEBUG] Script not found for ID:', id);
                reply.status(404);
                return { success: false, error: 'Incoming script not found' };
            }
        } catch (error) {
            console.error('❌ Error getting incoming campaign script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // Script management endpoints
    fastify.get('/api/scripts/incoming', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getIncomingScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentIncomingScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/api/scripts/outbound', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            return {
                success: true,
                scripts,
                current: scriptManager.getCurrentOutboundScript(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting outbound scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: ScriptIdParams }>('/api/scripts/incoming/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Incoming script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating incoming script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.post<{ Params: ScriptIdParams }>('/api/scripts/outbound/:scriptId/activate', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setOutboundScript(scriptId);

            if (success) {
                return {
                    success: true,
                    message: `Outbound script '${scriptId}' activated`,
                    scriptId,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return { success: false, error: 'Script not found' };
            }
        } catch (error) {
            console.error('❌ Error activating outbound script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    // === ADDITIONAL SCRIPT MANAGEMENT ENDPOINTS ===

    // Get all available outbound call scripts
    fastify.get('/api/outbound-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScriptId = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScriptId || 'default',
                    name: 'Current Script',
                    description: 'Currently active outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing outbound scripts:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Backward compatibility endpoint (returns outbound scripts)
    fastify.get('/api/incoming-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts();
            const currentScriptId = scriptManager.getCurrentOutboundScript();
            reply.send({
                success: true,
                scripts: scripts,
                currentScript: {
                    id: currentScriptId || 'default',
                    name: 'Current Script',
                    description: 'Currently active outbound script'
                }
            });
        } catch (error) {
            console.error('Error listing incoming scripts:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get current active incoming call script
    fastify.get('/api/incoming-scripts/current', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const currentScript = scriptManager.getCurrentIncomingScript();
            reply.send({
                success: true,
                currentScript: currentScript || {
                    id: 'customer-service',
                    name: 'Customer Service',
                    description: 'Default customer service script'
                }
            });
        } catch (error) {
            console.error('Error getting current incoming script:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Set active incoming call script
    fastify.post<{ Params: ScriptIdParams }>('/api/incoming-scripts/set/:scriptId', async (request, reply) => {
        try {
            const { scriptId } = request.params;

            if (!SecurityUtils.validateScriptId(scriptId)) {
                reply.status(400);
                return { success: false, error: 'Invalid script ID' };
            }

            const success = scriptManager.setIncomingScript(scriptId);

            if (success) {
                return reply.send({
                    success: true,
                    message: `Incoming script set to: ${scriptId}`,
                    scriptId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404);
                return reply.send({
                    success: false,
                    error: 'Script not found'
                });
            }
        } catch (error) {
            console.error('Error setting incoming script:', error);
            return reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    console.log('🔍 [DEBUG] About to register incoming scenarios route...');
    // Get all available incoming scenarios (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios route...');
    console.log('🔍 [DEBUG] scriptManager available:', !!scriptManager);
    console.log('🔍 [DEBUG] scriptManager.getIncomingScripts available:', typeof scriptManager?.getIncomingScripts);

    fastify.get('/api/incoming-scenarios', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            console.log('📥 [DEBUG] /api/incoming-scenarios called');
            console.log('🔍 [DEBUG] scriptManager in route:', !!scriptManager);
            const scenarios = scriptManager.getIncomingScripts();
            console.log('📊 [DEBUG] Found scenarios:', scenarios?.length || 0);
            reply.send({
                success: true,
                scenarios: scenarios.map((script: any) => ({
                    id: script.id,
                    name: script.name,
                    description: script.description,
                    category: script.category || 'support'
                })),
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting incoming scenarios:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });
    console.log('✅ /api/incoming-scenarios route registered successfully');

    // Select active incoming scenario (NEW SYSTEM)
    console.log('🔧 Registering /api/incoming-scenarios/select route...');
    fastify.post<{ Body: SelectScenarioBody }>('/api/incoming-scenarios/select', async (request, reply) => {
        try {
            const { scenarioId } = request.body;

            if (!scenarioId) {
                reply.status(400);
                return { success: false, error: 'Scenario ID is required' };
            }

            if (!SecurityUtils.validateScriptId(scenarioId)) {
                reply.status(400);
                return { success: false, error: 'Invalid scenario ID' };
            }

            const success = scriptManager.setIncomingScript(scenarioId);

            if (success) {
                return reply.send({
                    success: true,
                    message: `Incoming scenario selected: ${scenarioId}`,
                    scenarioId,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(404);
                return reply.send({
                    success: false,
                    error: 'Scenario not found'
                });
            }
        } catch (error) {
            console.error('Error selecting incoming scenario:', error);
            return reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });
    console.log('✅ /api/incoming-scenarios/select route registered successfully');

    // Configure incoming scenario (UI compatibility)
    console.log('🔧 Registering /api/configure-incoming-scenario route...');
    try {
        fastify.post<{ Body: ConfigureIncomingScenarioBody }>(
            '/api/configure-incoming-scenario',
            async (request, reply) => {
            try {
                console.log('📥 [DEBUG] configure-incoming-scenario called with body:', request.body);
                const { scenarioId, name, language, fromNumber, voice, model, country, script, isActive } = request.body;

                // Support both new scenarioId format and legacy format
                const targetScenarioId = scenarioId || name;

                if (!targetScenarioId) {
                    console.log('❌ [DEBUG] Missing scenarioId/name in request');
                    reply.status(400);
                    return { success: false, error: 'Scenario ID or name is required' };
                }

                console.log('🔍 [DEBUG] Using scenarioId:', targetScenarioId);

                if (!SecurityUtils.validateScriptId(targetScenarioId)) {
                    console.log('❌ [DEBUG] Invalid scenarioId:', targetScenarioId);
                    reply.status(400);
                    return { success: false, error: 'Invalid scenario ID' };
                }

                console.log('🔧 [DEBUG] Calling scriptManager.setIncomingScript...');
                const success = scriptManager.setIncomingScript(targetScenarioId);
                console.log('📊 [DEBUG] setIncomingScript result:', success);

                if (success) {
                    const result = {
                        success: true,
                        scenarioId: targetScenarioId,
                        message: `Incoming scenario '${targetScenarioId}' configured`,
                        timestamp: new Date().toISOString()
                    };
                    console.log('✅ [DEBUG] Returning success result:', result);
                    return result;
                } else {
                    console.log('❌ [DEBUG] setIncomingScript failed');
                    reply.status(404);
                    return { success: false, error: 'Scenario not found' };
                }
            } catch (error) {
                console.error('❌ Error configuring incoming scenario:', error);
                reply.status(500);
                return { success: false, error: (error as Error).message };
            }
        });
        console.log('✅ /api/configure-incoming-scenario route registered successfully');
    } catch (error) {
        console.error('❌ Error registering /api/configure-incoming-scenario route:', error);
    }

    // Legacy compatibility endpoints for tests

    // Incoming scripts endpoints
    fastify.get('/incoming-scripts', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const scripts = scriptManager.getOutboundScripts(); // Returns outbound scripts for compatibility
            return scripts.map((script: any) => ({
                id: script.id,
                name: script.name,
                description: script.description,
                systemPrompt: script.aiInstructions,
                campaignScript: script.script
            }));
        } catch (error) {
            console.error('❌ Error getting incoming scripts:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    fastify.get('/incoming-scripts/current', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const currentId = scriptManager.getCurrentOutboundScript(); // Returns outbound script for compatibility
            return {
                id: currentId || 'default',
                name: 'Current Script',
                systemPrompt: '',
                campaignScript: ''
            };
        } catch (error) {
            console.error('❌ Error getting current incoming script:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    console.log('✅ All script routes registered successfully!');
}