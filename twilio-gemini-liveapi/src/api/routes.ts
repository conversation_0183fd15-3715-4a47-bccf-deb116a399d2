// Main API routes for the Twilio Gemini service
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { TranscriptionManager } from '../audio/transcription-manager';
import { config } from '../config/config';
import { registerWebhookRoutes } from './webhook-routes';
import { registerSessionConfigRoutes } from './session-config-routes';
import { registerSessionLifecycleRoutes } from './session-lifecycle-routes';
import { registerScriptRoutes } from './script-routes';
import { registerAnalyticsRoutes } from './analytics-routes';
import { registerCallManagementRoutes } from './call-management-routes';
import { apiLogger } from '../utils/logger';
import { Dependencies } from '../types/api-types';
import { VerduonaAuthService } from '../auth/verduona-auth-service';

// Type definitions for routes not yet moved to shared types


export function registerApiRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('🚀 Starting API route registration...');
    console.log('🔍 [DEBUG] Dependencies received:', Object.keys(dependencies || {}));
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        recoveryManager,
        lifecycleManager,
        summaryManager,
        scriptManager,
        voiceManager,
        modelManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL,
        SUMMARY_GENERATION_PROMPT
    } = dependencies;
    console.log('✅ Dependencies extracted successfully');
    console.log('🔍 [DEBUG] scriptManager extracted:', !!scriptManager, typeof scriptManager);

    // Register webhook routes
    registerWebhookRoutes(fastify, dependencies);
    
    // Register session configuration routes
    registerSessionConfigRoutes(fastify, dependencies);
    
    // Register session lifecycle routes
    registerSessionLifecycleRoutes(fastify, dependencies);
    
    // Register script management routes
    registerScriptRoutes(fastify, dependencies);
    
    // Register analytics and metrics routes
    registerAnalyticsRoutes(fastify, dependencies);
    
    // Register call management routes
    registerCallManagementRoutes(fastify, dependencies);


    // Root route - API information
    fastify.get('/', async (_request: FastifyRequest, _reply: FastifyReply) => {
        return {
            service: 'Twilio Gemini Live API',
            status: 'running',
            version: '2.0.0',
            activeConnections: activeConnections.size,
            endpoints: {
                health: '/health',
                websocket: '/media-stream',
                localAudio: '/local-audio-session',
                voices: '/available-voices',
                models: '/available-models',
                providerHealth: '/api/provider-health',
                incomingScenarios: '/api/incoming-scenarios',
                configureIncomingScenario: '/api/configure-incoming-scenario'
            }
        };
    });

    // Health check route is already registered in index.ts - skip duplicate registration
    console.log('✅ Health check endpoints (skipped - already registered in index.ts)');





    // Get available Gemini voices
    fastify.get('/available-voices', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            return {
                success: true,
                voices: voiceConfig.availableVoices,
                currentDefault: voiceConfig.defaultVoice,
                voiceMapping: voiceConfig.voiceMapping,
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled,
                totalVoices: voiceConfig.totalVoices
            };
        } catch (error) {
            console.error('❌ Error getting available voices:', error);
            return {
                success: false,
                error: 'Failed to get available voices',
                message: (error as Error).message
            };
        }
    });

    // Voice Configuration Endpoint
    fastify.get('/api/voice-config', {
        schema: {
            response: {
                200: {
                    type: 'object',
                    properties: {
                        defaultVoice: { type: 'string' },
                        availableVoices: { type: 'object' },
                        voiceMapping: { type: 'object' },
                        voiceSelectionEnabled: { type: 'boolean' },
                        totalVoices: { type: 'number' },
                        voiceDescriptions: { type: 'object' }
                    }
                }
            }
        }
    }, async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            const envVoices = config?.ai?.gemini?.voices || {};

            // Parse voice descriptions from env
            const voiceDescriptions: Record<string, any> = {};
            if (envVoices && typeof envVoices === 'object') {
                Object.keys(envVoices).forEach(voiceName => {
                    const description = envVoices[voiceName];
                    voiceDescriptions[voiceName] = description;
                });
            }

            const response = {
                defaultVoice: voiceConfig.defaultVoice || 'Kore',
                availableVoices: voiceConfig.availableVoices || {},
                voiceMapping: voiceConfig.voiceMapping || {},
                voiceSelectionEnabled: voiceConfig.voiceSelectionEnabled || false,
                totalVoices: voiceConfig.totalVoices || 8,
                voiceDescriptions
            };

            return reply.code(200).send(response);
        } catch (error) {
            console.error('❌ Error getting voice config:', error);
            return reply.code(500).send({
                error: 'Failed to get voice configuration',
                details: (error as Error).message
            });
        }
    });

    // Get available Gemini models
    fastify.get('/available-models', async (_request: FastifyRequest, _reply: FastifyReply) => {
        try {
            const modelConfig = modelManager.getModelConfig();
            const availableModels = modelConfig.availableModels || {
                'gemini-2.5-flash-preview-native-audio-dialog': {
                    name: 'Gemini 2.5 Flash Preview Native Audio Dialog',
                    audioSupport: true
                },
                'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash Experimental', audioSupport: true }
            };
            
            return {
                success: true,
                availableModels: availableModels,
                defaultModel: modelConfig.defaultModel,
                currentModel: modelConfig.currentModel,
                modelSelectionEnabled: modelConfig.modelSelectionEnabled,
                totalModels: modelConfig.totalModels || Object.keys(availableModels).length,
                configurationSource: modelConfig.configurationSource
            };
        } catch (error) {
            console.error('❌ Error getting available models:', error);
            return {
                success: false,
                error: 'Failed to get available models',
                message: (error as Error).message
            };
        }
    });















    // Initialize Verduona Auth Service
    const authService = new VerduonaAuthService();

    // Token validation endpoint for production authentication
    fastify.post('/api/validate-token', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const body = request.body as { token?: string };
            const authHeader = request.headers.authorization;
            
            // Extract token from body or Authorization header
            const token = body?.token || (authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null);
            
            if (!token) {
                apiLogger.warn('🔐 Token validation failed: No token provided');
                return reply.code(401).send({
                    success: false,
                    error: 'No token provided'
                });
            }
            
            // Validate token with proper JWT verification
            const validationResult = await authService.validateToken(token);
            
            if (!validationResult.isValid) {
                apiLogger.warn('🔐 Token validation failed', {
                    error: validationResult.error,
                    tokenLength: token.length
                });
                return reply.code(401).send({
                    success: false,
                    error: validationResult.error || 'Invalid token'
                });
            }
            
            // Token is valid - extract user info
            const userInfo = validationResult.payload ?
                authService.getUserInfo(validationResult.payload) : null;
            
            apiLogger.info('🔐 Token validation successful', {
                user: userInfo?.id,
                email: userInfo?.email,
                roles: userInfo?.roles?.length || 0,
                tokenLength: token.length
            });
            
            return reply.code(200).send({
                success: true,
                message: 'Token is valid',
                user: userInfo,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            apiLogger.error('❌ Token validation error:', error as Error);
            return reply.code(500).send({
                success: false,
                error: 'Token validation failed',
                message: (error as Error).message
            });
        }
    });

    // Test route to verify route registration is working
    apiLogger.info('Registering test route...');
    try {
        fastify.get('/api/test', async (request: FastifyRequest, reply: FastifyReply) => {
            apiLogger.debug('Test route called');
            return { success: true, message: 'Test route working', timestamp: new Date().toISOString() };
        });
        apiLogger.info('Test route registered successfully');
    } catch (error) {
        apiLogger.error('Error registering test route', error as Error);
    }

    apiLogger.debug('Reached end of route registration function');

    apiLogger.info('All API routes registered successfully!');
}
