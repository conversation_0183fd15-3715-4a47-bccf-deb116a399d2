// Call management routes
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { Dependencies } from '../types/api-types';
import { SecurityUtils } from '../middleware/security-utils';
import twilio from 'twilio';
import { config } from '../config/config';

// Type definitions
interface MakeCallBody {
    to: string;
    from: string;
    task?: string;
    voice?: string;
    model?: string;
    targetName?: string;
    targetPhoneNumber?: string;
    outputLanguage?: string;
}

interface CallResultsParams {
    callSid: string;
}

interface CallConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName: string | null;
    targetPhoneNumber: string | null;
    phoneNumber: string;
    mode: string;
}

export function registerCallManagementRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    console.log('📞 Registering call management routes...');
    
    const {
        contextManager,
        lifecycleManager,
        scriptManager,
        GEMINI_DEFAULT_VOICE,
        GEMINI_DEFAULT_MODEL
    } = dependencies;

    // Configuration values
    const TWILIO_ACCOUNT_SID = config.auth.twilio.accountSid;
    const TWILIO_AUTH_TOKEN = config.auth.twilio.authToken;
    const PUBLIC_URL = config.server.publicUrl;

    // Make outbound call - Real Twilio integration
    fastify.post<{ Body: MakeCallBody }>('/make-call', {
        schema: {
            body: {
                type: 'object',
                required: ['to', 'from'],
                properties: {
                    to: { type: 'string' },
                    from: { type: 'string' },
                    task: { type: 'string' },
                    voice: { type: 'string' },
                    model: { type: 'string' },
                    targetName: { type: 'string' },
                    targetPhoneNumber: { type: 'string' },
                    outputLanguage: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { to, from, task, voice, model, targetName, targetPhoneNumber, outputLanguage } = request.body;

            // Input validation and sanitization
            const sanitizedTo = SecurityUtils.validatePhoneNumber(to);
            const sanitizedFrom = SecurityUtils.validatePhoneNumber(from);

            if (!sanitizedTo) {
                reply.status(400);
                return { error: 'Invalid "to" phone number format. Use international format (+**********).' };
            }

            if (!sanitizedFrom) {
                reply.status(400);
                return { error: 'Invalid "from" phone number format. Use international format (+**********).' };
            }

            const client = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

            // Get the current outbound script configuration
            const currentConfig = await (fastify as any).getNextCallConfig() || {
                aiInstructions: '',
                voice: GEMINI_DEFAULT_VOICE,
                model: GEMINI_DEFAULT_MODEL,
                targetName: null,
                targetPhoneNumber: null,
                phoneNumber: '',
                mode: 'outbound'
            };
            let callConfig = { ...currentConfig };
            
            // Try to get the active outbound script
            try {
                const currentScriptId = scriptManager.getCurrentOutboundScript();
                if (currentScriptId) {
                    const scriptConfig = await scriptManager.getScriptConfig(currentScriptId, false);
                    if (scriptConfig) {
                        callConfig = {
                            ...scriptConfig,
                            // Allow overrides from request
                            voice: voice || scriptConfig.voice,
                            model: model || scriptConfig.model,
                            targetName: targetName || '',
                            targetPhoneNumber: targetPhoneNumber || sanitizedTo,
                            phoneNumber: sanitizedTo,
                            aiInstructions: scriptConfig.aiInstructions,
                            mode: 'conversation'
                        } as CallConfig;
                        console.log(`📋 Using outbound script ${currentScriptId} for call`);
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error getting outbound script:', error);
            }
            
            // If task is provided, use it as a simple override (not recommended - use campaign scripts)
            if (task) {
                console.log('⚠️ Using task parameter as AI instructions (deprecated - use campaign scripts)');
                callConfig.aiInstructions = task;
            }
            
            // Update callConfig in webhook module with the final configuration
            (fastify as any).setNextCallConfig(callConfig);

            const call = await client.calls.create({
                to: sanitizedTo,
                from: sanitizedFrom,
                url: `${PUBLIC_URL}/incoming-call`,
                statusCallback: `${PUBLIC_URL}/call-status`,
                statusCallbackEvent: [
                    'initiated', 'ringing', 'answered', 'completed',
                    'failed', 'canceled', 'no-answer', 'busy'
                ],
                statusCallbackMethod: 'POST',
                record: true,
                recordingStatusCallback: `${PUBLIC_URL}/recording-status`,
                recordingStatusCallbackMethod: 'POST',
                recordingStatusCallbackEvent: ['completed']
            });

            console.log(`📞 [${call.sid}] Outbound call initiated to ${to}`);
            return { callSid: call.sid };

        } catch (error) {
            console.error('❌ Error making call:', error);
            reply.status(500);
            return { error: 'Failed to initiate call', details: (error as Error).message };
        }
    });

    // Get call results
    fastify.get<{ Params: CallResultsParams }>('/call-results/:callSid', async (request, reply) => {
        try {
            const { callSid } = request.params;

            if (!SecurityUtils.sanitizeCallSid(callSid)) {
                reply.status(404);
                return { success: false, error: 'Invalid call SID format' };
            }

            // Check if session exists in context manager
            const sessionContext = contextManager.getSessionContext(callSid);
            const sessionStatus = lifecycleManager ? lifecycleManager.getCurrentState(callSid) : null;

            if (sessionContext || sessionStatus) {
                return {
                    success: true,
                    callSid,
                    status: sessionStatus?.state || 'unknown',
                    context: sessionContext,
                    timestamp: new Date().toISOString()
                };
            } else {
                reply.status(404);
                return {
                    success: false,
                    error: 'Call results not found',
                    callSid
                };
            }
        } catch (error) {
            console.error('❌ Error getting call results:', error);
            reply.status(500);
            return { success: false, error: (error as Error).message };
        }
    });

    console.log('✅ Call management routes registered successfully');
}