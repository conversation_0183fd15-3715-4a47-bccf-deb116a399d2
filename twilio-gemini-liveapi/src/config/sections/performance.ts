import { ConfigValidator } from '../validator';

export const performance = {
    enableCaching: process.env.ENABLE_CACHING === 'true',
    cacheTimeout: ConfigValidator.validateNumber(process.env.CACHE_TIMEOUT, 'CACHE_TIMEOUT', 1, 86400, 300),
    maxConcurrentCalls: ConfigValidator.validateNumber(process.env.MAX_CONCURRENT_CALLS, 'MAX_CONCURRENT_CALLS', 1, 1000, 100),
    enableMetrics: process.env.ENABLE_METRICS === 'true'
};
