import { ConfigValidator } from '../validator';

export const audio = {
    inputFormat: process.env.INPUT_AUDIO_FORMAT || 'g711_ulaw',
    outputFormat: process.env.OUTPUT_AUDIO_FORMAT || 'g711_ulaw',
    sampleRate: ConfigValidator.validateNumber(process.env.SAMPLE_RATE, 'SAMPLE_RATE', 8000, 48000, 16000),
    twilioSampleRate: ConfigValidator.validateNumber(process.env.TWILIO_SAMPLE_RATE, 'TWILIO_SAMPLE_RATE', 8000, 48000, 8000),
    greetingAudioUrl: process.env.GREETING_AUDIO_URL
};
