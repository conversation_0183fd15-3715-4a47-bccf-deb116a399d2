import { ConfigValidator } from '../validator';

export const server = {
    port: ConfigValidator.validatePort(process.env.PORT, 'PORT', 3101),
    host: process.env.HOST || '0.0.0.0',
    environment: ConfigValidator.validateEnum(
        process.env.NODE_ENV,
        ['development', 'production', 'test'],
        'NODE_ENV',
        'development'
    ) as 'development' | 'production' | 'test',
    publicUrl:
        ConfigValidator.validateUrl(
            process.env.PUBLIC_URL ||
                `http://localhost:${process.env.TWILIO_GEMINI_BACKEND_PORT || process.env.PORT || 3101}`,
            'PUBLIC_URL',
            true
        ) ||
        `http://localhost:${process.env.TWILIO_GEMINI_BACKEND_PORT || process.env.PORT || 3101}`,
    corsOrigin: process.env.CORS_ORIGIN || process.env.PUBLIC_URL || '*',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
};
