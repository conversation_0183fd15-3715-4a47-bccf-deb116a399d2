import path from 'path';
import { fileURLToPath } from 'url';
import { ConfigValidator } from '../validator';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const campaigns = {
    scriptsPath: process.env.CAMPAIGN_SCRIPTS_PATH || path.join(__dirname, '../../../call-center-frontend/public'),
    totalCampaigns: ConfigValidator.validateNumber(process.env.TOTAL_CAMPAIGNS, 'TOTAL_CAMPAIGNS', 1, 100, 6),
    defaultCampaignId: ConfigValidator.validateNumber(process.env.DEFAULT_CAMPAIGN_ID, 'DEFAULT_CAMPAIGN_ID', 1, 100, 1),
    enableCustomScripts: process.env.ENABLE_CUSTOM_SCRIPTS === 'true',
    scriptCacheTimeout: ConfigValidator.validateNumber(process.env.SCRIPT_CACHE_TIMEOUT, 'SCRIPT_CACHE_TIMEOUT', 0, 86400, 300)
};
