export const prompts = {
    summaryGeneration: process.env.SUMMARY_GENERATION_PROMPT || 'Report campaign related result and important context or follow up.',
    aiPrepareOutbound: process.env.AI_PREPARE_MESSAGE_OUTBOUND || '',
    incomingCallGreeting: process.env.INCOMING_CALL_GREETING || 'Hello, thank you for calling. How may I assist you today?',
    aiPrepareIncoming: process.env.AI_PREPARE_MESSAGE_INCOMING || 'Hello, thank you for calling. How may I assist you today?',
    systemMessageBio: process.env.SYSTEM_MESSAGE_BIO || '',
    systemMessageVoicePersonality: process.env.SYSTEM_MESSAGE_VOICE_PERSONALITY || '',
    systemMessageVoiceSpeed: process.env.SYSTEM_MESSAGE_VOICE_SPEED || ''
};
