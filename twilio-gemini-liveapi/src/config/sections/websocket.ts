import { ConfigValidator } from '../validator';

export const websocket = {
    protocol: process.env.WS_PROTOCOL || 'wss',
    url: process.env.WEBSOCKET_URL,
    heartbeatInterval: ConfigValidator.validateNumber(
        process.env.HEARTBEAT_INTERVAL,
        'HEARTBEAT_INTERVAL',
        1000,
        120000,
        30000
    ),
    heartbeatTimeout: ConfigValidator.validateNumber(
        process.env.HEARTBEAT_TIMEOUT,
        'HEARTBEAT_TIMEOUT',
        1000,
        60000,
        10000
    )
};
