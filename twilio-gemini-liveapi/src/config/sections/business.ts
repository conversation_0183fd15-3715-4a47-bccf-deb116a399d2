import { ConfigValidator } from '../validator';

export const business = {
    callTimeouts: {
        default: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_DEFAULT, 'CALL_TIMEOUT_DEFAULT', 1, 3600, 30),
        intro: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_INTRO, 'CALL_TIMEOUT_INTRO', 1, 60, 4),
        response: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_RESPONSE, 'CALL_TIMEOUT_RESPONSE', 1, 60, 10)
    },
    validation: {
        maxVehicles: ConfigValidator.validateNumber(process.env.MAX_VEHICLES, 'MAX_VEHICLES', 1, 50, 9),
        maxClaims: ConfigValidator.validateNumber(process.env.MAX_CLAIMS, 'MAX_CLAIMS', 0, 20, 3),
        minVehicleYear: ConfigValidator.validateNumber(process.env.MIN_VEHICLE_YEAR, 'MIN_VEHICLE_YEAR', 1900, 2030, 1900),
        maxVehicleYear: ConfigValidator.validateNumber(process.env.MAX_VEHICLE_YEAR, 'MAX_VEHICLE_YEAR', 1900, 2030, 2027)
    },
    transfer: {
        defaultTransferNumber: process.env.DEFAULT_TRANSFER_NUMBER || '555-123-4567',
        defaultAgentName: process.env.DEFAULT_AGENT_NAME || 'Sarah Johnson',
        transferTimeout: ConfigValidator.validateNumber(process.env.TRANSFER_TIMEOUT, 'TRANSFER_TIMEOUT', 1, 300, 30)
    }
};
