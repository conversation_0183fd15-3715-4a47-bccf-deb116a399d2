export const voices = {
    defaultVoiceMapping: {
        en: {
            incoming: process.env.VOICE_EN_INCOMING || 'Kore',
            outbound: process.env.VOICE_EN_OUTBOUND || 'Aoede'
        },
        es: {
            incoming: process.env.VOICE_ES_INCOMING || 'Kore',
            outbound: process.env.VOICE_ES_OUTBOUND || 'Puck'
        },
        cz: {
            incoming: process.env.VOICE_CZ_INCOMING || 'Charon',
            outbound: process.env.VOICE_CZ_OUTBOUND || 'Fenrir'
        }
    },
    enableVoiceSelection: process.env.ENABLE_VOICE_SELECTION === 'true'
};
