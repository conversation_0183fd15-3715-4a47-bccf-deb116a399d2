import { ConfigValidator } from '../validator';

export const auth = {
    gemini: {
        apiKey: ConfigValidator.validateRequired(process.env.GEMINI_API_KEY, 'GEMINI_API_KEY')
    },
    openai: {
        apiKey: process.env.OPENAI_API_KEY,
        apiUrl: process.env.OPENAI_API_URL || 'https://api.openai.com'
    },
    twilio: {
        accountSid: ConfigValidator.validateRequired(process.env.TWILIO_ACCOUNT_SID, 'TWILIO_ACCOUNT_SID'),
        authToken: ConfigValidator.validateRequired(process.env.TWILIO_AUTH_TOKEN, 'TWILIO_AUTH_TOKEN')
    },
    deepgram: {
        apiKey: process.env.DEEPGRAM_API_KEY || ''
    },
    supabase: {
        url: process.env.SUPABASE_URL || '',
        anonKey: process.env.SUPABASE_ANON_KEY || ''
    },
    ngrok: {
        authToken: process.env.NGROK_AUTHTOKEN
    }
};
