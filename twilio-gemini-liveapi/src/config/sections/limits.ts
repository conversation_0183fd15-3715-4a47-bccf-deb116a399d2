// Limits configuration section
import { ConfigValidator } from '../validator.js';

export const limits = {
    // Audio limits
    audioClippingThreshold: ConfigValidator.validateNumber(
        process.env.AUDIO_CLIPPING_THRESHOLD,
        'AUDIO_CLIPPING_THRESHOLD',
        1,
        null,
        30000
    ),
    audioSilenceThreshold: ConfigValidator.validateNumber(
        process.env.AUDIO_SILENCE_THRESHOLD,
        'AUDIO_SILENCE_THRESHOLD',
        1,
        null,
        10000
    ),
    
    // Security limits
    maxPayloadSize: ConfigValidator.validateNumber(
        process.env.MAX_PAYLOAD_SIZE,
        'MAX_PAYLOAD_SIZE',
        1,
        null,
        10485760 // 10MB
    ),
    maxRateLimitRequests: ConfigValidator.validateNumber(
        process.env.MAX_RATE_LIMIT_REQUESTS,
        'MAX_RATE_LIMIT_REQUESTS',
        1,
        null,
        5
    ),
    
    // Script limits
    maxScriptLength: ConfigValidator.validateNumber(
        process.env.MAX_SCRIPT_LENGTH,
        'MAX_SCRIPT_LENGTH',
        1,
        null,
        10000
    ),
    
    // Retry limits
    maxRetryDelay: ConfigValidator.validateNumber(
        process.env.MAX_RETRY_DELAY,
        'MAX_RETRY_DELAY',
        1,
        null,
        30000
    ),
    
    // Recovery limits
    maxRecoveryAttempts: ConfigValidator.validateNumber(
        process.env.MAX_RECOVERY_ATTEMPTS,
        'MAX_RECOVERY_ATTEMPTS',
        1,
        null,
        3
    ),
    recoveryJitterPercent: parseFloat(process.env.RECOVERY_JITTER_PERCENT || '0.1'), // 10% jitter
    
    // Performance limits
    performanceErrorThreshold: ConfigValidator.validateNumber(
        process.env.PERFORMANCE_ERROR_THRESHOLD,
        'PERFORMANCE_ERROR_THRESHOLD',
        1,
        null,
        3
    )
};