export class ConfigValidator {
    static validateRequired(value: string | undefined, name: string): string {
        if (!value) {
            throw new Error(`Required configuration missing: ${name}`);
        }
        return value;
    }

    static validateUrl(value: string | undefined, name: string, required: boolean = false): string | undefined {
        if (!value && required) {
            throw new Error(`Required URL configuration missing: ${name}`);
        }
        if (value && !value.match(/^https?:\/\/.+/)) {
            throw new Error(`Invalid URL format for ${name}: ${value}`);
        }
        return value;
    }

    static validatePort(value: string | undefined, name: string, defaultValue: number = 3000): number {
        const port = parseInt(value || '') || defaultValue;
        if (port < 1 || port > 65535) {
            throw new Error(`Invalid port for ${name}: ${port}`);
        }
        return port;
    }

    static validateEnum(value: string | undefined, validValues: string[], name: string, defaultValue: string | null = null): string {
        if (!value && defaultValue) {
            return defaultValue;
        }
        if (!value || !validValues.includes(value)) {
            throw new Error(`Invalid value for ${name}: ${value}. Valid values: ${validValues.join(', ')}`);
        }
        return value;
    }

    static validateNumber(value: string | undefined, name: string, min: number | null = null, max: number | null = null, defaultValue: number | null = null): number {
        if (!value && defaultValue !== null) {
            return defaultValue;
        }
        const num = parseFloat(value || '');
        if (isNaN(num)) {
            throw new Error(`Invalid number for ${name}: ${value}`);
        }
        if (min !== null && num < min) {
            throw new Error(`${name} must be >= ${min}, got ${num}`);
        }
        if (max !== null && num > max) {
            throw new Error(`${name} must be <= ${max}, got ${num}`);
        }
        return num;
    }
}
