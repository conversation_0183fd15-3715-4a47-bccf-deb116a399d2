/**
 * Production-safe logger to replace console.log statements
 * Prevents excessive logging in production while maintaining debug capabilities
 */

import { logger } from './logger';

export class ProductionLogger {
    private isDevelopment = process.env.NODE_ENV !== 'production';
    private isDebugEnabled = process.env.DEBUG_LOGGING === 'true';
    
    /**
     * Info level logging - always shown
     */
    info(message: string, ...args: any[]): void {
        logger.info(message, ...args);
    }
    
    /**
     * Debug level logging - only in development or when debug enabled
     */
    debug(message: string, ...args: any[]): void {
        if (this.isDevelopment || this.isDebugEnabled) {
            logger.debug(message, ...args);
        }
    }
    
    /**
     * Warning level logging - always shown
     */
    warn(message: string, ...args: any[]): void {
        logger.warn(message, ...args);
    }
    
    /**
     * Error level logging - always shown
     */
    error(message: string, ...args: any[]): void {
        logger.error(message, ...args);
    }
    
    /**
     * Replace console.log with proper logging
     * @deprecated Use debug() instead
     */
    log(message: string, ...args: any[]): void {
        this.debug(`[CONSOLE] ${message}`, ...args);
    }
    
    /**
     * Critical system events that should always be logged
     */
    system(message: string, ...args: any[]): void {
        logger.info(`[SYSTEM] ${message}`, ...args);
    }
    
    /**
     * Performance metrics that should be tracked
     */
    performance(message: string, metrics: Record<string, any>): void {
        if (this.isDevelopment || process.env.PERFORMANCE_LOGGING === 'true') {
            logger.info(`[PERF] ${message}`, metrics);
        }
    }
    
    /**
     * Security-related events that should always be logged
     */
    security(message: string, ...args: any[]): void {
        logger.warn(`[SECURITY] ${message}`, ...args);
    }
}

export const productionLogger = new ProductionLogger();

// Convenience exports for common patterns
export const debugLog = productionLogger.debug.bind(productionLogger);
export const infoLog = productionLogger.info.bind(productionLogger);
export const warnLog = productionLogger.warn.bind(productionLogger);
export const errorLog = productionLogger.error.bind(productionLogger);
export const systemLog = productionLogger.system.bind(productionLogger);
export const perfLog = productionLogger.performance.bind(productionLogger);
export const securityLog = productionLogger.security.bind(productionLogger);