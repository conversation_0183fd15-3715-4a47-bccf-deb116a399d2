/**
 * Buffer Pool for Audio Processing
 * Reuses buffers to reduce garbage collection and improve performance
 */

export class BufferPool {
    private static instance: BufferPool;
    private pools: Map<number, Buffer[]> = new Map();
    private maxPoolSize: number = 50;
    private maxBufferSize: number = 32768; // 32KB max buffer size

    private constructor() {}

    static getInstance(): BufferPool {
        if (!BufferPool.instance) {
            BufferPool.instance = new BufferPool();
        }
        return BufferPool.instance;
    }

    /**
     * Get a buffer from the pool or create a new one
     */
    acquire(size: number): Buffer {
        // Don't pool buffers larger than max size
        if (size > this.maxBufferSize) {
            return Buffer.alloc(size);
        }

        const pool = this.pools.get(size);
        if (pool && pool.length > 0) {
            const buffer = pool.pop()!;
            buffer.fill(0); // Clear the buffer
            return buffer;
        }

        return Buffer.alloc(size);
    }

    /**
     * Return a buffer to the pool
     */
    release(buffer: Buffer): void {
        const size = buffer.length;
        
        // Don't pool buffers larger than max size
        if (size > this.maxBufferSize) {
            return;
        }

        let pool = this.pools.get(size);
        if (!pool) {
            pool = [];
            this.pools.set(size, pool);
        }

        // Don't exceed max pool size
        if (pool.length < this.maxPoolSize) {
            pool.push(buffer);
        }
    }

    /**
     * Get statistics about the buffer pool
     */
    getStats(): { poolCount: number; totalBuffers: number; sizes: number[] } {
        let totalBuffers = 0;
        const sizes: number[] = [];

        for (const [size, pool] of this.pools.entries()) {
            totalBuffers += pool.length;
            sizes.push(size);
        }

        return {
            poolCount: this.pools.size,
            totalBuffers,
            sizes
        };
    }

    /**
     * Clear all pools
     */
    clear(): void {
        this.pools.clear();
    }
}

// Float32Array pool for audio processing
export class Float32ArrayPool {
    private static instance: Float32ArrayPool;
    private pools: Map<number, Float32Array[]> = new Map();
    private maxPoolSize: number = 20;
    private maxArraySize: number = 16384; // 16K samples max

    private constructor() {}

    static getInstance(): Float32ArrayPool {
        if (!Float32ArrayPool.instance) {
            Float32ArrayPool.instance = new Float32ArrayPool();
        }
        return Float32ArrayPool.instance;
    }

    /**
     * Get a Float32Array from the pool or create a new one
     */
    acquire(length: number): Float32Array {
        // Don't pool arrays larger than max size
        if (length > this.maxArraySize) {
            return new Float32Array(length);
        }

        const pool = this.pools.get(length);
        if (pool && pool.length > 0) {
            const array = pool.pop()!;
            array.fill(0); // Clear the array
            return array;
        }

        return new Float32Array(length);
    }

    /**
     * Return a Float32Array to the pool
     */
    release(array: Float32Array): void {
        const length = array.length;
        
        // Don't pool arrays larger than max size
        if (length > this.maxArraySize) {
            return;
        }

        let pool = this.pools.get(length);
        if (!pool) {
            pool = [];
            this.pools.set(length, pool);
        }

        // Don't exceed max pool size
        if (pool.length < this.maxPoolSize) {
            pool.push(array);
        }
    }

    /**
     * Clear all pools
     */
    clear(): void {
        this.pools.clear();
    }
}

// Export singleton instances
export const bufferPool = BufferPool.getInstance();
export const float32ArrayPool = Float32ArrayPool.getInstance();