// Standardized API Response Formatter
// Provides consistent error and success response formats across all API endpoints

export interface StandardResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: string[];
  message?: string;
  timestamp: string;
  requestId?: string;
  code?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface PaginatedResponse<T> extends StandardResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  requestId?: string
): StandardResponse<T> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
  error: string | Error,
  code?: string,
  requestId?: string,
  statusCode?: number
): StandardResponse {
  const errorMessage = error instanceof Error ? error.message : error;
  
  return {
    success: false,
    error: errorMessage,
    code: code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Creates a validation error response
 */
export function createValidationErrorResponse(
  errors: ValidationError[],
  requestId?: string
): StandardResponse {
  return {
    success: false,
    error: 'Validation failed',
    errors: errors.map(e => `${e.field}: ${e.message}`),
    code: 'VALIDATION_ERROR',
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Creates a paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string,
  requestId?: string
): PaginatedResponse<T> {
  return {
    success: true,
    data,
    message,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    },
    timestamp: new Date().toISOString(),
    requestId
  };
}

/**
 * Format error for API responses with proper HTTP status codes
 */
export function formatApiError(
  error: any,
  defaultStatusCode = 500,
  requestId?: string
): { response: StandardResponse; statusCode: number } {
  let statusCode = defaultStatusCode;
  let errorCode = 'INTERNAL_ERROR';
  let errorMessage = 'Internal server error';

  if (error instanceof Error) {
    errorMessage = error.message;
    
    // Map specific error types to HTTP status codes
    if (error.name === 'ValidationError') {
      statusCode = 400;
      errorCode = 'VALIDATION_ERROR';
    } else if (error.name === 'UnauthorizedError') {
      statusCode = 401;
      errorCode = 'UNAUTHORIZED';
    } else if (error.name === 'ForbiddenError') {
      statusCode = 403;
      errorCode = 'FORBIDDEN';
    } else if (error.name === 'NotFoundError') {
      statusCode = 404;
      errorCode = 'NOT_FOUND';
    } else if (error.name === 'ConflictError') {
      statusCode = 409;
      errorCode = 'CONFLICT';
    } else if (error.name === 'RateLimitError') {
      statusCode = 429;
      errorCode = 'RATE_LIMIT_EXCEEDED';
    }
  }

  // Check if error has custom status code
  if (typeof error.statusCode === 'number') {
    statusCode = error.statusCode;
  }

  const response = createErrorResponse(errorMessage, errorCode, requestId);
  
  return { response, statusCode };
}

/**
 * Middleware function to standardize all API responses
 */
export function responseFormatterMiddleware() {
  return {
    success: createSuccessResponse,
    error: createErrorResponse,
    validationError: createValidationErrorResponse,
    paginated: createPaginatedResponse,
    formatError: formatApiError
  };
}

/**
 * Health check response format
 */
export function createHealthResponse(
  status: 'healthy' | 'unhealthy' | 'degraded',
  checks: Record<string, { status: string; message?: string; timestamp: string }>,
  requestId?: string
): StandardResponse {
  return {
    success: status === 'healthy',
    data: {
      status,
      checks,
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0'
    },
    timestamp: new Date().toISOString(),
    requestId
  };
}