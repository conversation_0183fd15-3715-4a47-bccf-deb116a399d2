/**
 * Memory monitoring and management utilities for production stability
 */

import { logger } from './logger';
import { timerManager } from './timer-manager';

export interface MemoryStats {
    rss: number;          // Resident Set Size
    heapTotal: number;    // Total heap size
    heapUsed: number;     // Used heap size
    external: number;     // External memory usage
    arrayBuffers: number; // ArrayBuffer memory usage
}

export interface MemoryThresholds {
    warning: number;      // Warning threshold (MB)
    critical: number;     // Critical threshold (MB)
    maxHeapUsed: number;  // Max heap used threshold (MB)
}

export class MemoryMonitor {
    private static instance: MemoryMonitor;
    private thresholds: MemoryThresholds;
    private isMonitoring = false;
    private lastGC = 0;
    private gcThreshold = 100; // MB
    private monitoringInterval = 30000; // 30 seconds

    private constructor() {
        this.thresholds = {
            warning: parseInt(process.env.MEMORY_WARNING_THRESHOLD || '800', 10), // 800MB
            critical: parseInt(process.env.MEMORY_CRITICAL_THRESHOLD || '950', 10), // 950MB
            maxHeapUsed: parseInt(process.env.MEMORY_MAX_HEAP_THRESHOLD || '700', 10) // 700MB
        };
    }

    static getInstance(): MemoryMonitor {
        if (!MemoryMonitor.instance) {
            MemoryMonitor.instance = new MemoryMonitor();
        }
        return MemoryMonitor.instance;
    }

    /**
     * Get current memory usage statistics
     */
    getMemoryStats(): MemoryStats {
        const usage = process.memoryUsage();
        return {
            rss: Math.round(usage.rss / 1024 / 1024),
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
            heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
            external: Math.round(usage.external / 1024 / 1024),
            arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024)
        };
    }

    /**
     * Check if memory usage is within safe limits
     */
    checkMemoryHealth(): { status: 'ok' | 'warning' | 'critical'; stats: MemoryStats; message?: string } {
        const stats = this.getMemoryStats();

        if (stats.rss > this.thresholds.critical || stats.heapUsed > this.thresholds.maxHeapUsed) {
            return {
                status: 'critical',
                stats,
                message: `Critical memory usage: RSS ${stats.rss}MB, Heap ${stats.heapUsed}MB`
            };
        }

        if (stats.rss > this.thresholds.warning) {
            return {
                status: 'warning',
                stats,
                message: `High memory usage: RSS ${stats.rss}MB`
            };
        }

        return { status: 'ok', stats };
    }

    /**
     * Force garbage collection if available
     */
    forceGarbageCollection(): boolean {
        if (global.gc) {
            try {
                const beforeStats = this.getMemoryStats();
                global.gc();
                const afterStats = this.getMemoryStats();
                
                const freed = beforeStats.heapUsed - afterStats.heapUsed;
                logger.info('🗑️ Forced garbage collection', {
                    freedMB: freed,
                    beforeHeap: beforeStats.heapUsed,
                    afterHeap: afterStats.heapUsed
                });
                
                this.lastGC = Date.now();
                return true;
            } catch (error) {
                logger.error('❌ Error during garbage collection:', error as Error);
                return false;
            }
        }
        return false;
    }

    /**
     * Start memory monitoring
     */
    startMonitoring(): void {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;
        logger.info('📊 Starting memory monitoring', {
            interval: this.monitoringInterval,
            thresholds: this.thresholds
        });

        timerManager.setInterval('memory_monitor', () => {
            const health = this.checkMemoryHealth();
            
            if (health.status === 'critical') {
                logger.error('🚨 Critical memory usage detected', health);
                
                // Force GC if we haven't done it recently
                const timeSinceLastGC = Date.now() - this.lastGC;
                if (timeSinceLastGC > 60000) { // 1 minute
                    this.forceGarbageCollection();
                }
                
                // Emit warning for external monitoring
                // @ts-ignore - Custom event
                process.emit('memoryWarning', health);
                
            } else if (health.status === 'warning') {
                logger.warn('⚠️ High memory usage detected', health);
                
                // Consider GC if heap usage is high
                if (health.stats.heapUsed > this.gcThreshold) {
                    const timeSinceLastGC = Date.now() - this.lastGC;
                    if (timeSinceLastGC > 120000) { // 2 minutes
                        this.forceGarbageCollection();
                    }
                }
            } else {
                // Log memory stats periodically in debug mode
                if (process.env.DEBUG_LEVEL === 'debug') {
                    logger.debug('📊 Memory stats', health.stats);
                }
            }
        }, this.monitoringInterval);
    }

    /**
     * Stop memory monitoring
     */
    stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        timerManager.clearInterval('memory_monitor');
        logger.info('📊 Memory monitoring stopped');
    }

    /**
     * Get memory usage summary for health checks
     */
    getHealthSummary(): {
        status: string;
        memoryMB: number;
        heapUsedMB: number;
        uptime: number;
        thresholds: MemoryThresholds;
    } {
        const health = this.checkMemoryHealth();
        return {
            status: health.status,
            memoryMB: health.stats.rss,
            heapUsedMB: health.stats.heapUsed,
            uptime: Math.round(process.uptime()),
            thresholds: this.thresholds
        };
    }

    /**
     * Clean up large objects and arrays
     */
    static cleanupLargeObjects(obj: any): void {
        if (!obj || typeof obj !== 'object') {
            return;
        }

        try {
            // Clear arrays
            if (Array.isArray(obj)) {
                obj.length = 0;
                return;
            }

            // Clear object properties that are arrays or large objects
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const value = obj[key];
                    
                    if (Array.isArray(value)) {
                        value.length = 0;
                    } else if (value && typeof value === 'object') {
                        // Recursively clean nested objects
                        MemoryMonitor.cleanupLargeObjects(value);
                    }
                }
            }
        } catch (error) {
            logger.warn('⚠️ Error during object cleanup:', error as Error);
        }
    }

    /**
     * Emergency memory cleanup
     */
    emergencyCleanup(): void {
        logger.warn('🚨 Performing emergency memory cleanup');
        
        // Force garbage collection
        this.forceGarbageCollection();
        
        // Emit cleanup event for other modules to respond
        // @ts-ignore - Custom event
        process.emit('emergencyMemoryCleanup');
        
        logger.info('✅ Emergency memory cleanup completed');
    }
}

// Export singleton instance
export const memoryMonitor = MemoryMonitor.getInstance();

// Add process event listeners for memory warnings
process.on('memoryWarning', (health: any) => {
    logger.error('🚨 Memory warning received', health);
    
    // In production, consider restarting if memory is critically high
    if (process.env.NODE_ENV === 'production' && health.status === 'critical') {
        logger.error('🚨 Critical memory usage in production - consider restart');
        
        // Give some time for cleanup before potential restart
        setTimeout(() => {
            if (memoryMonitor.checkMemoryHealth().status === 'critical') {
                logger.error('🚨 Memory still critical after cleanup - exiting');
                process.exit(1);
            }
        }, 30000); // 30 seconds
    }
});

// Start monitoring automatically in production
if (process.env.NODE_ENV === 'production') {
    memoryMonitor.startMonitoring();
}
