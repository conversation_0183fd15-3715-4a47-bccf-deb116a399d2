/**
 * Timer Management Utility
 * Prevents memory leaks by tracking and cleaning up timers and intervals
 */

export class TimerManager {
    private static instance: TimerManager;
    private timers: Map<string, NodeJS.Timeout> = new Map();
    private intervals: Map<string, NodeJS.Timeout> = new Map();

    private constructor() {}

    static getInstance(): TimerManager {
        if (!TimerManager.instance) {
            TimerManager.instance = new TimerManager();
        }
        return TimerManager.instance;
    }

    /**
     * Create a managed timeout
     */
    setTimeout(id: string, callback: () => void, delay: number): NodeJS.Timeout {
        // Clear existing timer with same ID
        this.clearTimeout(id);

        const timer = setTimeout(() => {
            callback();
            this.timers.delete(id);
        }, delay);

        this.timers.set(id, timer);
        return timer;
    }

    /**
     * Create a managed interval
     */
    setInterval(id: string, callback: () => void, delay: number): NodeJS.Timeout {
        // Clear existing interval with same ID
        this.clearInterval(id);

        const interval = setInterval(callback, delay);
        this.intervals.set(id, interval);
        return interval;
    }

    /**
     * Clear a specific timeout
     */
    clearTimeout(id: string): void {
        const timer = this.timers.get(id);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(id);
        }
    }

    /**
     * Clear a specific interval
     */
    clearInterval(id: string): void {
        const interval = this.intervals.get(id);
        if (interval) {
            clearInterval(interval);
            this.intervals.delete(id);
        }
    }

    /**
     * Clear all timers for a specific session
     */
    clearSessionTimers(sessionId: string): void {
        const sessionPrefix = `${sessionId}_`;
        
        // Clear timers
        for (const [id, timer] of this.timers.entries()) {
            if (id.startsWith(sessionPrefix)) {
                clearTimeout(timer);
                this.timers.delete(id);
            }
        }

        // Clear intervals
        for (const [id, interval] of this.intervals.entries()) {
            if (id.startsWith(sessionPrefix)) {
                clearInterval(interval);
                this.intervals.delete(id);
            }
        }
    }

    /**
     * Clear all timers and intervals
     */
    clearAll(): void {
        // Clear all timers
        for (const [id, timer] of this.timers.entries()) {
            clearTimeout(timer);
        }
        this.timers.clear();

        // Clear all intervals
        for (const [id, interval] of this.intervals.entries()) {
            clearInterval(interval);
        }
        this.intervals.clear();
    }

    /**
     * Get statistics about active timers
     */
    getStats(): { timers: number; intervals: number; activeIds: string[] } {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            activeIds: [...this.timers.keys(), ...this.intervals.keys()]
        };
    }
}

// Export singleton instance
export const timerManager = TimerManager.getInstance();