/**
 * BoundedMap provides a Map with a maximum size.
 * Old entries are cleaned up when capacity is exceeded.
 */
import { logger } from './logger';
import type { ConnectionData } from '../types/global';

export class BoundedMap<K, V> extends Map<K, V> {
  private maxSize: number;
  private lifecycleManagerRef: any = null;
  private operationLock: boolean = false;

  constructor(maxSize: number = 2000) {
    super();
    this.maxSize = maxSize;
  }

  setLifecycleManager(lifecycleManagerRef: any): void {
    this.lifecycleManagerRef = lifecycleManagerRef;
  }

  private isConnectionData(value: unknown): value is ConnectionData {
    return value !== null && 
           typeof value === 'object' && 
           'sessionId' in value;
  }

  override set(key: K, value: V): this {
    // Simple mutex to prevent concurrent modifications
    if (this.operationLock) {
      // Wait briefly and retry if locked
      return this.setWithDelay(key, value);
    }

    this.operationLock = true;
    try {
      if (this.size >= this.maxSize && !this.has(key)) {
        const firstKey = this.keys().next().value as K;
        const oldValue = this.get(firstKey);

        if (oldValue && typeof oldValue === 'object') {
          logger.warn(
            `⚠️ BoundedMap: Forcing cleanup of oldest entry ${String(firstKey)} (capacity: ${this.maxSize})`,
            {
              sessionId: firstKey,
              mapSize: this.size,
              maxSize: this.maxSize,
              valueType: typeof oldValue,
              timestamp: new Date().toISOString()
            }
          );

          // Check if value has twilioWs property (for ConnectionData type)
          if (this.isConnectionData(oldValue) && oldValue.twilioWs) {
            try {
              oldValue.twilioWs.close();
            } catch (e) {
              logger.error(`Error closing WebSocket for ${String(firstKey)}:`, e as Error);
            }
          }

          if (this.lifecycleManagerRef && typeof this.lifecycleManagerRef.endSession === 'function') {
            this.lifecycleManagerRef
              .endSession(firstKey, oldValue, 'capacity_limit')
              .catch((err: any) => {
                logger.error(`Error ending session ${String(firstKey)}:`, err);
              });
          }
        }

        this.delete(firstKey);
      }
      return super.set(key, value);
    } finally {
      this.operationLock = false;
    }
  }

  private setWithDelay(key: K, value: V): this {
    // Simple retry mechanism with short delay
    setTimeout(() => {
      if (!this.operationLock) {
        this.set(key, value);
      } else {
        this.setWithDelay(key, value);
      }
    }, 1);
    return this;
  }
}
