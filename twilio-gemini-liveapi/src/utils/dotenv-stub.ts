import fs from 'fs';
import path from 'path';
import { configLogger as logger } from './logger';

export function config(): void {
  try {
    const cwd = process.cwd();
    const envPath = path.resolve(cwd, '.env');
    const examplePath = path.resolve(cwd, '.env.example');

    const fileToLoad = fs.existsSync(envPath) ? envPath : fs.existsSync(examplePath) ? examplePath : null;

    if (!fileToLoad) {
      return;
    }

    let data: string;
    try {
      data = fs.readFileSync(fileToLoad, 'utf8');
    } catch (readError) {
      logger.error(`❌ Failed to read environment file ${fileToLoad}:`, readError instanceof Error ? readError : new Error(String(readError)));
      return;
    }

    for (const line of data.split('\n')) {
      const match = line.match(/^\s*([\w.-]+)\s*=\s*(.*)\s*$/);
      if (match) {
        const key = match[1];
        let value = match[2] || '';

        if (value.startsWith('"') && value.endsWith('"')) {
          value = value.slice(1, -1);
        }

        if (!(key in process.env)) {
          process.env[key] = value;
        }
      }
    }
  } catch (e) {
    logger.warn('dotenv-stub: failed to load', { error: e instanceof Error ? e.message : String(e) });
  }
}

export default { config };