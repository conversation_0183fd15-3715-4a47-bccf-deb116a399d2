import { ContextManager } from './context-manager';
import { sessionLogger } from '../utils/logger';
import { SessionMetrics } from './metrics';

export async function recoverSession(
    callSid: string,
    reason: string,
    contextManager: ContextManager,
    sessionMetrics: Map<string, SessionMetrics>,
    recoverySet: Set<string>
): Promise<void> {
    if (recoverySet.has(callSid)) {
        sessionLogger.info(`⏳ [${callSid}] Recovery already in progress`);
        return;
    }
    recoverySet.add(callSid);
    try {
        const context = contextManager.getSessionContext(callSid);
        if (!context || !contextManager.canRecover(callSid)) {
            sessionLogger.info(`❌ [${callSid}] Cannot recover session`);
            return;
        }
        const recoveryCount = contextManager.incrementRecoveryAttempt(callSid);
        sessionLogger.info(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
            metrics.lastRecoveryTime = Date.now();
        }
        sessionLogger.info(`✅ [${callSid}] Recovery preparation completed`);
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error during session recovery:`, error instanceof Error ? error : new Error(String(error)));
    } finally {
        recoverySet.delete(callSid);
    }
}
