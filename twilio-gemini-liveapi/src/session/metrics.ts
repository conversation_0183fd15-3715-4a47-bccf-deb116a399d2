export interface SessionMetrics {
    startTime: number;
    messagesReceived: number;
    messagesSent: number;
    recoveryCount: number;
    lastActivity: number;
    isInitializing: boolean;
    lastRecoveryTime?: number;
}

import { sessionLogger } from '../utils/logger';

export class BoundedMap<K, V> extends Map<K, V> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    set(key: K, value: V): this {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            if (firstKey !== undefined) {
                const oldValue = this.get(firstKey);
                this.delete(firstKey);
                sessionLogger.info(`🧹 SessionManager BoundedMap: Removed oldest entry ${String(firstKey)}`, {
                    mapSize: this.size,
                    maxSize: this.maxSize,
                    removedKey: firstKey,
                    removedValueType: typeof oldValue
                });
            }
        }
        return super.set(key, value);
    }
}

export class BoundedSet<T> extends Set<T> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    add(value: T): this {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            if (firstValue !== undefined) {
                this.delete(firstValue);
                sessionLogger.info(`🧹 SessionManager BoundedSet: Removed oldest entry ${String(firstValue)}`, {
                    setSize: this.size,
                    maxSize: this.maxSize,
                    removedValue: firstValue
                });
            }
        }
        return super.add(value);
    }
}

export function addToBoundedArray<T>(array: T[], item: T, maxSize: number): T[] {
    if (!Array.isArray(array)) {
        sessionLogger.warn('addToBoundedArray: array is not an array, initializing as empty array');
        array = [];
    }

    array.push(item);

    if (array.length > maxSize) {
        const removed = array.splice(0, array.length - maxSize);
        sessionLogger.info(`🧹 Trimmed ${removed.length} old entries from bounded array (max: ${maxSize})`);
    }

    return array;
}
