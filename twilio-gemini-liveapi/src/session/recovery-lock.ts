/**
 * Recovery Lock Manager
 * Provides thread-safe locking mechanism for session recovery operations
 */

import { recoveryLogger } from '../utils/logger';

export class RecoveryLockManager {
    private locks: Map<string, {
        timestamp: number;
        promiseResolve?: (value: boolean) => void;
        timeoutId?: NodeJS.Timeout;
    }> = new Map();
    
    private pendingLockRequests: Map<string, Array<(value: boolean) => void>> = new Map();
    private readonly lockTimeout: number;
    
    constructor(lockTimeout = 30000) {
        this.lockTimeout = lockTimeout;
    }
    
    /**
     * Attempts to acquire a lock for the given key
     * Returns true if lock acquired, false if already locked
     */
    async acquireLock(key: string): Promise<boolean> {
        return new Promise((resolve) => {
            // Check if lock exists and is still valid
            const existingLock = this.locks.get(key);
            if (existingLock && Date.now() - existingLock.timestamp < this.lockTimeout) {
                // Lock is held, add to pending queue
                const pending = this.pendingLockRequests.get(key) || [];
                pending.push(resolve);
                this.pendingLockRequests.set(key, pending);
                
                recoveryLogger.debug(`🔒 Lock request queued`, { 
                    key, 
                    queueLength: pending.length,
                    lockAge: Date.now() - existingLock.timestamp
                });
                return;
            }
            
            // Clean up expired lock if exists
            if (existingLock) {
                this.releaseLock(key);
            }
            
            // Acquire new lock
            const timeoutId = setTimeout(() => {
                recoveryLogger.warn(`⏰ Lock timeout`, { key });
                this.releaseLock(key);
            }, this.lockTimeout);
            
            this.locks.set(key, {
                timestamp: Date.now(),
                promiseResolve: resolve,
                timeoutId
            });
            
            recoveryLogger.debug(`🔒 Lock acquired`, { key });
            resolve(true);
        });
    }
    
    /**
     * Releases a lock and processes any pending requests
     */
    releaseLock(key: string): void {
        const lock = this.locks.get(key);
        if (!lock) {
            return;
        }
        
        // Clear timeout if exists
        if (lock.timeoutId) {
            clearTimeout(lock.timeoutId);
        }
        
        // Delete the lock
        this.locks.delete(key);
        recoveryLogger.debug(`🔓 Lock released`, { key });
        
        // Process pending requests
        const pending = this.pendingLockRequests.get(key);
        if (pending && pending.length > 0) {
            const nextRequest = pending.shift()!;
            this.pendingLockRequests.set(key, pending);
            
            // Give the next request the lock
            const timeoutId = setTimeout(() => {
                recoveryLogger.warn(`⏰ Lock timeout (pending)`, { key });
                this.releaseLock(key);
            }, this.lockTimeout);
            
            this.locks.set(key, {
                timestamp: Date.now(),
                timeoutId
            });
            
            recoveryLogger.debug(`🔒 Lock transferred to pending request`, { 
                key,
                remainingPending: pending.length
            });
            
            nextRequest(true);
        } else {
            // No pending requests, clean up
            this.pendingLockRequests.delete(key);
        }
    }
    
    /**
     * Checks if a lock is currently held
     */
    isLocked(key: string): boolean {
        const lock = this.locks.get(key);
        return !!(lock && Date.now() - lock.timestamp < this.lockTimeout);
    }
    
    /**
     * Gets the current lock status
     */
    getLockStatus(): {
        activeLocks: number;
        pendingRequests: number;
        locks: Array<{
            key: string;
            age: number;
            hasPending: boolean;
        }>;
    } {
        const locks = Array.from(this.locks.entries()).map(([key, lock]) => ({
            key,
            age: Date.now() - lock.timestamp,
            hasPending: (this.pendingLockRequests.get(key) || []).length > 0
        }));
        
        const totalPending = Array.from(this.pendingLockRequests.values())
            .reduce((sum, pending) => sum + pending.length, 0);
        
        return {
            activeLocks: this.locks.size,
            pendingRequests: totalPending,
            locks
        };
    }
    
    /**
     * Cleans up all locks and pending requests
     */
    cleanup(): void {
        // Clear all timeouts
        for (const [key, lock] of this.locks.entries()) {
            if (lock.timeoutId) {
                clearTimeout(lock.timeoutId);
            }
        }
        
        // Reject all pending requests
        for (const [key, pending] of this.pendingLockRequests.entries()) {
            pending.forEach(resolve => resolve(false));
        }
        
        this.locks.clear();
        this.pendingLockRequests.clear();
        
        recoveryLogger.info(`🧹 Lock manager cleaned up`);
    }
}

// Export a singleton instance
export const recoveryLockManager = new RecoveryLockManager();