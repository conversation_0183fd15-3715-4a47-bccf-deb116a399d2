// Session Summary Manager - handles session summary generation and storage
import { writeFile, readFile, access, mkdir } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { ContextManager } from './context-manager';
import { ConnectionData } from '../types/global';
import { ExtendedConnectionData } from '../types/websocket';
import { generateLocalSummary } from './summary-generator';
import { sessionLogger } from '../utils/logger';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

type FlowType = 'outbound_call' | 'inbound_call' | 'outbound_test' | 'inbound_test' | 'default';

interface SummaryPrompts {
    outbound_call: string;
    inbound_call: string;
    outbound_test: string;
    inbound_test: string;
    default: string;
}

interface SummaryStatus {
    inProgress: boolean;
    hasTimeout: boolean;
    timestamp: number;
}

export class SessionSummaryManager {
    private summaryTimeouts: Map<string, NodeJS.Timeout>;
    private summaryInProgress: Set<string>;
    private defaultSummaryTimeout: number;
    private summaryPrompts: SummaryPrompts;

    constructor() {
        this.summaryTimeouts = new Map(); // Track summary timeouts
        this.summaryInProgress = new Set(); // Track sessions currently generating summaries
        this.defaultSummaryTimeout = 30000; // 30 seconds timeout for summary generation (increased from 15s)

        // Summary prompts - use environment variable only, no hardcoded fallbacks
        this.summaryPrompts = {
            outbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            outbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            default: process.env.SUMMARY_GENERATION_PROMPT || ''
        };
    }

    /**
     * Request session summary from AI with flow-specific prompts
     * @param callSid - Call/session ID
     * @param connectionData - Connection data with Gemini session
     * @param contextManager - Context manager for saving summary state
     * @returns Success status
     */
    async requestSummary(callSid: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<boolean> {
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        if (!extConnectionData || extConnectionData.summaryRequested || extConnectionData.summaryReceived) {
            sessionLogger.debug(`[${callSid}] Summary request skipped (already requested/received or no connection data)`);
            return false;
        }

        if (this.summaryInProgress.has(callSid)) {
            sessionLogger.debug(`[${callSid}] Summary generation already in progress`);
            return false;
        }

        // Determine flow type for appropriate summary prompt
        const flowType = this.determineFlowType(extConnectionData);
        const summaryPrompt = this.getSummaryPrompt(flowType);

        sessionLogger.info(`[${callSid}] Requesting ${flowType} session summary from AI`);
        extConnectionData.summaryRequested = true;
        extConnectionData.summaryText = '';
        extConnectionData.summaryFlowType = flowType;
        this.summaryInProgress.add(callSid);

        try {
            // Check if we have a valid Gemini session
            if (!extConnectionData.geminiSession) {
                sessionLogger.warn(`[${callSid}] No active Gemini session for summary request`);
                return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
            }

            // Set timeout for summary generation
            const timeoutId = setTimeout(async () => {
                sessionLogger.warn(`[${callSid}] Summary generation timeout - using fallback`);
                await this.handleSummaryTimeout(callSid, extConnectionData, contextManager);
            }, this.defaultSummaryTimeout);

            extConnectionData.summaryTimeoutId = timeoutId;
            this.summaryTimeouts.set(callSid, timeoutId);

            // Send flow-specific summary request to AI using Live API - FIXED METHOD
            // Use sendRealtimeInput with proper text data format
            await extConnectionData.geminiSession.sendRealtimeInput({
                media: {
                    data: Buffer.from(summaryPrompt, 'utf-8').toString('base64'),
                    mimeType: 'text/plain'
                }
            });

            sessionLogger.info(`[${callSid}] ${flowType} summary request sent to AI successfully`);
            return true;

        } catch (error) {
            sessionLogger.error(`[${callSid}] Error requesting summary from AI`, error as Error);
            this.summaryInProgress.delete(callSid);
            return await this.generateFallbackSummary(callSid, extConnectionData, contextManager);
        }
    }

    /**
     * Determine the flow type from connection data
     * @param connectionData - Connection data
     * @returns Flow type
     */
    private determineFlowType(connectionData: ExtendedConnectionData): FlowType {
        if (connectionData.flowType) {
            return connectionData.flowType as FlowType;
        }

        if (connectionData.sessionType === 'local_test') {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }

        if (connectionData.sessionType === 'twilio_call') {
            return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
        }

        // Fallback determination
        if (connectionData.isTestMode) {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }

        return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
    }

    /**
     * Get appropriate summary prompt for flow type
     * @param flowType - Flow type
     * @returns Summary prompt
     */
    private getSummaryPrompt(flowType: FlowType): string {
        return this.summaryPrompts[flowType] || this.summaryPrompts.default;
    }

    /**
     * Handle summary response from AI
     * @param callSid - Call/session ID
     * @param summaryText - Summary text from AI
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    async handleSummaryResponse(callSid: string, summaryText: string, connectionData: ConnectionData, contextManager: ContextManager): Promise<void> {
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        if (!this.summaryInProgress.has(callSid)) {
            sessionLogger.warn(`[${callSid}] Received summary but no generation in progress`);
            return;
        }

        sessionLogger.info(`[${callSid}] Received summary from AI (${summaryText.length} characters)`);
        
        // Clear timeout
        this.clearSummaryTimeout(callSid, extConnectionData);

        // Save summary
        await this.saveSummaryInfo(callSid, summaryText, 'neutral', 'completed', 
            extConnectionData.targetName, extConnectionData.targetPhoneNumber);

        // Mark as received
        extConnectionData.summaryReceived = true;
        extConnectionData.summaryText = summaryText;
        this.summaryInProgress.delete(callSid);

        // Update context
        if (contextManager) {
            const context = contextManager.getSessionContext(callSid);
            if (context) {
                context.conversationState.summaryReceived = true;
                context.conversationState.summaryText = summaryText;
                contextManager.contextStore.set(callSid, context);
            }
        }

        sessionLogger.info(`[${callSid}] Summary processed and saved successfully`);
    }

    /**
     * Generate fallback summary from conversation data with flow-specific formatting
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     * @returns Success status
 */
    private async generateFallbackSummary(callSid: string, connectionData: ExtendedConnectionData, contextManager: ContextManager): Promise<boolean> {
        sessionLogger.info(`[${callSid}] Generating fallback summary from conversation data`);

        try {
            const flowType = this.determineFlowType(connectionData);
            const prompt = this.getSummaryPrompt(flowType);
            const summary = generateLocalSummary(connectionData.conversationLog || [], prompt);

            await this.saveSummaryInfo(callSid, summary, 'neutral', 'completed',
                connectionData.targetName, connectionData.targetPhoneNumber, flowType);

            connectionData.summaryReceived = true;
            connectionData.summaryText = summary;
            connectionData.summaryFlowType = flowType;
            this.summaryInProgress.delete(callSid);

            sessionLogger.info(`[${callSid}] ${flowType} fallback summary generated and saved successfully`);
            return true;

        } catch (error) {
            sessionLogger.error(`[${callSid}] Error generating fallback summary`, error as Error);
            this.summaryInProgress.delete(callSid);
            return false;
        }
    }

    /**
     * Handle summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param contextManager - Context manager
     */
    private async handleSummaryTimeout(callSid: string, connectionData: ExtendedConnectionData, contextManager: ContextManager): Promise<void> {
        sessionLogger.warn(`[${callSid}] Summary generation timed out`);
        
        this.clearSummaryTimeout(callSid, connectionData);
        
        if (connectionData.summaryText && connectionData.summaryText.length > 0) {
            // Use partial summary if available
            sessionLogger.info(`[${callSid}] Using partial summary received before timeout`);
            await this.saveSummaryInfo(callSid, connectionData.summaryText, 'neutral', 'timeout',
                connectionData.targetName, connectionData.targetPhoneNumber);
        } else {
            // Generate fallback summary
            await this.generateFallbackSummary(callSid, connectionData, contextManager);
        }

        connectionData.summaryReceived = true;
        this.summaryInProgress.delete(callSid);
    }

    /**
     * Clear summary timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    private clearSummaryTimeout(callSid: string, connectionData: ExtendedConnectionData): void {
        const timeoutId = this.summaryTimeouts.get(callSid);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.summaryTimeouts.delete(callSid);
        }
        
        if (connectionData && connectionData.summaryTimeoutId) {
            clearTimeout(connectionData.summaryTimeoutId);
            connectionData.summaryTimeoutId = undefined;
        }
    }

    /**
     * Save summary information to file with flow type support
     * @param callSid - Call/session ID
     * @param rawSummaryText - Raw summary text
     * @param defaultSentiment - Default sentiment
     * @param status - Call status
     * @param targetName - Target name
     * @param targetPhoneNumber - Target phone number
     * @param flowType - Flow type (outbound_call, inbound_call, etc.)
     */
    async saveSummaryInfo(callSid: string, rawSummaryText: string, defaultSentiment: 'positive' | 'negative' | 'neutral' = 'neutral', 
                          status: string = 'completed', targetName: string | null = null, targetPhoneNumber: string | null = null, 
                          flowType: string | null = null): Promise<void> {
        const dataDir = path.join(process.cwd(), 'data');
        const infoFilePath = path.join(dataDir, `${callSid}_info.json`);
        
        sessionLogger.info(`[${callSid}] Saving summary info. Status: ${status}, Target: ${targetName}`);
        sessionLogger.debug(`[${callSid}] Summary text: "${rawSummaryText?.substring(0, 100)}..." (length: ${rawSummaryText?.length || 0})`);

        let summary = 'Summary generation failed or was not applicable.';
        let sentiment: 'positive' | 'negative' | 'neutral' = defaultSentiment;

        // Parse summary and sentiment
        if (rawSummaryText && status !== 'incomplete' && status !== 'error' && !['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            const sentimentMatch = rawSummaryText.match(/sentiment:\s*(positive|neutral|negative)/i);
            if (sentimentMatch && sentimentMatch[1]) {
                sentiment = sentimentMatch[1].toLowerCase() as typeof sentiment;
                summary = rawSummaryText.replace(/sentiment:\s*(positive|neutral|negative)/i, '').trim();
                console.log(`🎭 [${callSid}] Parsed sentiment: ${sentiment}`);
            } else {
                summary = rawSummaryText.trim();
            }
        } else if (rawSummaryText) {
            summary = rawSummaryText;
        } else if (['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            summary = `Call ended with status: ${status}. No summary generated.`;
            sentiment = 'neutral';
        } else if (status === 'timeout') {
            summary = 'Summary generation timed out.';
            sentiment = 'neutral';
        } else if (status === 'completed' && targetName) {
            summary = `Call completed with ${targetName}. AI summary generation was not successful, but the call connected and ended normally.`;
            sentiment = 'neutral';
        }

        const summaryData = {
            callSid: callSid,
            call_summary: summary,
            customer_sentiment: sentiment,
            status: status,
            targetName: targetName || 'Unknown',
            targetPhoneNumber: targetPhoneNumber || 'Unknown',
            timestamp: new Date().toISOString()
        };

        try {
            // Ensure data directory exists
            await mkdir(dataDir, { recursive: true });

            // Read existing data if available
            let existingData: any = {};
            try {
                const existingContent = await readFile(infoFilePath, 'utf8');
                existingData = JSON.parse(existingContent);
            } catch (readError: any) {
                if (readError.code !== 'ENOENT') {throw readError;}
            }

            // Merge new data with existing data (preserve recordingUrl, etc.)
            const finalData = { ...existingData, ...summaryData };

            await writeFile(infoFilePath, JSON.stringify(finalData, null, 2));
            console.log(`✅ [${callSid}] Summary info saved to ${infoFilePath}`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error saving summary info:`, error);
        }
    }

    /**
     * Check if summary is in progress
     * @param callSid - Call/session ID
     * @returns True if summary is in progress
     */
    isSummaryInProgress(callSid: string): boolean {
        return this.summaryInProgress.has(callSid);
    }

    /**
     * Get summary status
     * @param callSid - Call/session ID
     * @returns Summary status
     */
    getSummaryStatus(callSid: string): SummaryStatus {
        return {
            inProgress: this.summaryInProgress.has(callSid),
            hasTimeout: this.summaryTimeouts.has(callSid),
            timestamp: Date.now()
        };
    }

    /**
     * Clean up summary tracking for a session
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    cleanupSummary(callSid: string, connectionData: ConnectionData): void {
        this.clearSummaryTimeout(callSid, connectionData as ExtendedConnectionData);
        this.summaryInProgress.delete(callSid);
        sessionLogger.debug(`[${callSid}] Summary tracking cleaned up`);
    }

    /**
     * Clean up all summary resources (for shutdown)
     */
    cleanup(): void {
        const timeoutCount = this.summaryTimeouts.size;
        const progressCount = this.summaryInProgress.size;

        // Clear all summary timeouts
        for (const [callSid, timeout] of this.summaryTimeouts.entries()) {
            clearTimeout(timeout);
        }

        this.summaryTimeouts.clear();
        this.summaryInProgress.clear();

        sessionLogger.info(`SummaryManager: Cleared ${timeoutCount} timeouts and ${progressCount} in-progress summaries`);
    }
}