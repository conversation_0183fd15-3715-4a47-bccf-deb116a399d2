import { forwardAudio } from '../audio/audio-forwarding';
import { AudioProcessor } from '../audio/audio-processor';
import { sessionLogger } from '../utils/logger';
import type { ConnectionData, GeminiLiveMessage, ConversationEntry } from '../types/global';

// Extended connection data interface (removed empty interface to fix linting)
type ExtendedConnectionData = ConnectionData & {
    sessionReady?: boolean;
    sessionInitialized?: number;
};

interface MessageRouterDeps {
    audioProcessor: AudioProcessor;
    activeConnections?: Map<string, ConnectionData> | null;
    sessionMetrics?: Map<string, { messagesReceived: number; lastActivity: number }>;
    forwardAudioFn?: typeof forwardAudio;
}

export async function routeGeminiMessage(
    callSid: string,
    message: GeminiLiveMessage,
    connectionData: ExtendedConnectionData,
    deps: MessageRouterDeps
): Promise<void> {
    const {
        audioProcessor,
        activeConnections,
        sessionMetrics,
        forwardAudioFn = forwardAudio
    } = deps;

    try {
        const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
        if (!message || !message.serverContent) {
            sessionLogger.warn(`⚠️ [${callSid}] Received invalid message structure`);
            return;
        }

        const metrics = sessionMetrics?.get(callSid);
        if (metrics) {
            metrics.messagesReceived = (metrics.messagesReceived || 0) + 1;
            metrics.lastActivity = Date.now();
        }

        if (hasAudio && hasAudio.mimeType && hasAudio.mimeType.includes('audio')) {
            if (!hasAudio.data || hasAudio.data.length === 0) {
                sessionLogger.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
            } else {
                sessionLogger.info(`🎤 [${callSid}] Received audio from Gemini`, {
                    dataLength: hasAudio.data.length,
                    mimeType: hasAudio.mimeType,
                    sessionType: connectionData.sessionType,
                    isTwilioCall: connectionData.isTwilioCall,
                    audioForwardingEnabled: connectionData.audioForwardingEnabled,
                    lastAudioSent: connectionData.lastAudioSent
                });
                
                const fresh = activeConnections?.get(callSid) || connectionData;
                
                // CRITICAL FIX: Check if session is ready for audio processing
                const sessionReady = (fresh as any).sessionReady;
                const wsConnected = !!(fresh.ws || fresh.twilioWs || fresh.localWs);
                const audioForwardingEnabled = fresh.audioForwardingEnabled;
                
                // Log the fresh connection data state for debugging
                sessionLogger.debug(`🔍 [${callSid}] Fresh connection data state`, {
                    sessionId: fresh.sessionId,
                    sessionType: fresh.sessionType,
                    isTwilioCall: fresh.isTwilioCall,
                    streamSid: fresh.streamSid,
                    sequenceNumber: fresh.sequenceNumber,
                    sessionReady: sessionReady,
                    audioForwardingEnabled: audioForwardingEnabled,
                    wsConnected: wsConnected
                });
                
                // If session isn't ready or WebSocket not connected, log and continue
                if (!sessionReady || !wsConnected) {
                    sessionLogger.warn(`⚠️ [${callSid}] Session not fully ready, buffering audio`, {
                        sessionReady: sessionReady,
                        wsConnected: wsConnected,
                        audioForwardingEnabled: audioForwardingEnabled
                    });
                }

                if (!audioForwardingEnabled) {
                    sessionLogger.warn(`⚠️ [${callSid}] Audio forwarding not enabled, buffering audio`);
                }
                
                const forwardingResult = await forwardAudioFn(callSid, hasAudio, fresh, audioProcessor);
                
                if (forwardingResult) {
                    sessionLogger.debug(`✅ [${callSid}] Audio forwarding successful`);
                } else {
                    sessionLogger.error(`❌ [${callSid}] Audio forwarding failed - AI audio lost!`, {
                        hasAudio: !!hasAudio,
                        hasConnectionData: !!fresh,
                        hasAudioProcessor: !!audioProcessor
                    });
                }
            }
        }

        const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
        if (text) {
            connectionData.lastAIResponse = Date.now();
            connectionData.responseTimeouts = 0;
            connectionData.connectionQuality = 'good';

            if (connectionData.conversationLog) {
                const MAX_LOG_SIZE = 500;
                connectionData.conversationLog.push({ role: 'assistant', content: text, timestamp: Date.now() });
                if (connectionData.conversationLog.length > MAX_LOG_SIZE) {
                    connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_LOG_SIZE);
                }
            }

            if (connectionData.summaryRequested) {
                connectionData.summaryText = (connectionData.summaryText || '') + text;
            }
        }
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error processing Gemini message:`, error instanceof Error ? error : new Error(String(error)));
    }
}
