# Structured Outputs Overview

This file is based on the Google Gemini documentation for Structured Outputs. It describes how to enforce JSON Schema when generating model responses.

Key points:
- Responses follow the schema you provide.
- Errors are surfaced programmatically when the model refuses.
- SDKs for Python and JavaScript simplify schema definitions with Pydantic or Zod.

Example using the JavaScript SDK:
```ts
import { GoogleGenAI } from '@google/genai';
import { z } from 'zod';

const genai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const CalendarEvent = z.object({
  name: z.string(),
  date: z.string(),
  participants: z.array(z.string()),
});

const completion = await openai.beta.chat.completions.parse({
  model: 'gpt-4o-mini-2024-07-18',
  messages: [
    { role: 'system', content: 'Extract the event information.' },
    { role: 'user', content: '<PERSON> and <PERSON> are going to a science fair on Friday.' },
  ],
  response_format: zodResponseFormat(CalendarEvent, 'event'),
});

const event = completion.choices[0].message.parsed;
```

See the official Gemini documentation for more details.
