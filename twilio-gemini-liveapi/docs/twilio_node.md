# Twilio Node Quickstart Summary

This document summarizes the steps from the Twilio blog post **"Build a real-time speech assistant using Twilio and OpenAI Realtime API"**. Refer to the original article for full instructions.

## Setup Steps
1. Install Node.js 18+ and create a new project directory.
2. Run `npm init -y` and install dependencies:
   ```bash
   npm install fastify ws dotenv @fastify/formbody @fastify/websocket
   ```
3. Create a `.env` file containing your `OPENAI_API_KEY`.
4. Implement the server logic to proxy audio between Twilio and Gemini.
5. Start the server with `node index.js`.

This repository includes a reference implementation under `src/`. The above summary is adapted from the official Twilio tutorial.
