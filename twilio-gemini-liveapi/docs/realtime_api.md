# Realtime API Highlights

These notes are adapted from the Google Gemini Realtime API documentation. They outline how to configure sessions and handle function calls.

- Use `session.update` to set default tools and audio settings.
- Supported events include `response.audio.delta`, `session.updated`, and `rate_limits.updated`.
- Functions are provided as tools in the same format as the Chat Completions API.

Example snippet:
```json
{
  "type": "session.update",
  "session": {
    "turn_detection": {"type": "server_vad"},
    "input_audio_format": "g711_ulaw",
    "output_audio_format": "g711_ulaw"
  }
}
```

Refer to Google's official documentation for comprehensive instructions and examples.
